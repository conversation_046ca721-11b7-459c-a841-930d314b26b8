{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/navigation/navbar/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import Image from \"next/image\";\r\nimport React, { useState } from \"react\";\r\nimport { Menu, X, BellDot, Globe, ChevronDown, CircleUser } from \"lucide-react\";\r\n\r\nconst Navbar = ({ onToggleSidebar }: { onToggleSidebar?: () => void }) => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n\r\n  const toggleMobileMenu = () => {\r\n    setIsMobileMenuOpen(!isMobileMenuOpen);\r\n  };\r\n\r\n  return (\r\n    <header className=\"border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0\">\r\n      <div className=\"flex items-center justify-between\">\r\n        {/* Left side with menu button and logo */}\r\n        <div className=\"flex items-center gap-3\">\r\n          {/* Sidebar toggle button for mobile/tablet */}\r\n          <button\r\n            onClick={onToggleSidebar}\r\n            className=\"lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n            aria-label=\"Toggle sidebar\"\r\n          >\r\n            <Menu className=\"h-5 w-5 text-gray-600\" />\r\n          </button>\r\n\r\n          <div className=\"text-lg sm:text-xl font-semibold text-gray-900\">\r\n            AI Interview\r\n          </div>\r\n        </div>\r\n\r\n        {/* Desktop Navigation */}\r\n        <div className=\"hidden lg:flex items-center gap-4 xl:gap-6\">\r\n          {/* Notification Icon */}\r\n          <BellDot className=\"h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors\" />\r\n\r\n          {/* Language Selector */}\r\n          <div className=\"flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors\">\r\n            <Globe className=\"h-5 w-5 sm:h-6 sm:w-6\" />\r\n            <span className=\"font-bold hidden sm:inline\">English</span>\r\n            <ChevronDown className=\"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]\" />\r\n          </div>\r\n\r\n          {/* Profile Section */}\r\n          <div className=\"flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors\">\r\n            {/* Avatar */}\r\n            <div className=\"h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n              <span className=\"text-xs sm:text-sm font-medium text-gray-500 p-1\"></span>\r\n            </div>\r\n\r\n            {/* User Info */}\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"text-xs sm:text-sm font-bold text-gray-900\">\r\n                Hammad M\r\n              </span>\r\n              <span className=\"text-xs text-purple-600\">Free</span>\r\n            </div>\r\n\r\n            {/* Dropdown Arrow */}\r\n            <ChevronDown className=\"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tablet Navigation (768px - 1024px) */}\r\n        <div className=\"hidden md:flex lg:hidden items-center gap-3\">\r\n          {/* Notification Icon */}\r\n          <BellDot className=\"h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors\" />\r\n\r\n          {/* Compact Profile */}\r\n          <div className=\"flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors\">\r\n            <div className=\"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center\">\r\n              <span className=\"text-xs font-medium text-gray-500\">H</span>\r\n            </div>\r\n            <span className=\"text-sm font-bold text-gray-900\">Hammad</span>\r\n            <ChevronDown className=\"h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile menu toggle - only on small screens */}\r\n        <div className=\"sm:hidden\">\r\n          <button onClick={toggleMobileMenu} className=\"p-2\">\r\n            {isMobileMenuOpen ? (\r\n              <X className=\"h-6 w-6\" />\r\n            ) : (\r\n              <CircleUser className=\"h-6 w-6 text-gray-700\" />\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile dropdown - only on small screens */}\r\n      {isMobileMenuOpen && (\r\n        <div className=\"mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4\">\r\n          <div className=\"flex items-center gap-3 p-3 rounded-lg bg-gray-50\">\r\n            <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n              <span className=\"text-sm font-medium text-gray-500\">H</span>\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"text-sm font-bold text-gray-900\">Hammad M</span>\r\n              <span className=\"text-xs text-purple-600\">Free</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\">\r\n            <BellDot className=\"h-5 w-5 text-gray-700\" />\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              Notifications\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\">\r\n            <Globe className=\"h-5 w-5 text-gray-700\" />\r\n            <span className=\"text-sm font-medium text-gray-700\">English</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AAEA,kCAAkC;AAClC;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AAMA,MAAM,SAAS,CAAC,EAAE,eAAe,EAAoC;;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;0CAAiD;;;;;;;;;;;;kCAMlE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;;;;;;;;;;kDAIlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA6C;;;;;;0DAG7D,6LAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;kDAI5C,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK3B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;kDAEtD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;kDAClD,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAO,SAAS;4BAAkB,WAAU;sCAC1C,iCACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAO7B,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;kDAClD,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;GAjHM;KAAA;uCAmHS", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/images/logo-light.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 177, height: 62, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/sideBar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport LOGO from \"../public/images/logo-light.svg\";\r\nimport { LayoutDashboard, BriefcaseBusiness, X } from \"lucide-react\";\r\n\r\nconst sidebarItems = [\r\n  {\r\n    label: \"Dashboard\",\r\n    href: \"/\",\r\n    icon: LayoutDashboard,\r\n  },\r\n  {\r\n    label: \"Job Posts\",\r\n    href: \"/interview\",\r\n    icon: BriefcaseBusiness,\r\n  },\r\n];\r\n\r\nconst Sidebar = ({\r\n  isOpen,\r\n  onClose,\r\n}: {\r\n  isOpen?: boolean;\r\n  onClose?: () => void;\r\n}) => {\r\n  const pathname = usePathname();\r\n  const previousPathname = useRef(pathname);\r\n\r\n  // Close mobile sidebar when route changes (but not on initial mount)\r\n  useEffect(() => {\r\n    if (previousPathname.current !== pathname && isOpen && onClose) {\r\n      onClose();\r\n    }\r\n    previousPathname.current = pathname;\r\n  }, [pathname, isOpen, onClose]);\r\n\r\n  // Close mobile sidebar when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const sidebar = document.getElementById(\"mobile-sidebar\");\r\n      const overlay = document.getElementById(\"sidebar-overlay\");\r\n      if (\r\n        sidebar &&\r\n        !sidebar.contains(event.target as Node) &&\r\n        overlay?.contains(event.target as Node)\r\n      ) {\r\n        if (onClose) onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"unset\";\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n      document.body.style.overflow = \"unset\";\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop Sidebar */}\r\n      <aside className=\"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0\">\r\n        {/* Logo */}\r\n        <div className=\"flex items-center gap-2 mb-10\">\r\n          <Image src={LOGO} alt=\"Logo\" />\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex flex-col gap-4\">\r\n          {sidebarItems.map((item) => {\r\n            const isActive = pathname === item.href;\r\n            const Icon = item.icon;\r\n\r\n            return (\r\n              <Link\r\n                key={item.label}\r\n                href={item.href}\r\n                className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\r\n                  ${\r\n                    isActive\r\n                      ? \"bg-purple-100 text-purple-700 font-extrabold\"\r\n                      : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"\r\n                  }`}\r\n              >\r\n                <Icon\r\n                  className={`w-5 h-5 ${\r\n                    isActive ? \"text-purple-700\" : \"text-gray-400\"\r\n                  }`}\r\n                />\r\n                <span className=\"text-sm font-medium\">{item.label}</span>\r\n              </Link>\r\n            );\r\n          })}\r\n        </nav>\r\n      </aside>\r\n\r\n      {/* Mobile/Tablet Sidebar Overlay */}\r\n      <div\r\n        id=\"sidebar-overlay\"\r\n        className={`fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ${\r\n          isOpen ? \"opacity-100\" : \"opacity-0 pointer-events-none\"\r\n        }`}\r\n      >\r\n        <aside\r\n          id=\"mobile-sidebar\"\r\n          className={`fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ${\r\n            isOpen ? \"translate-x-0\" : \"-translate-x-full\"\r\n          }`}\r\n        >\r\n          {/* Mobile Sidebar Header */}\r\n          <div className=\"flex items-center justify-between mb-10\">\r\n            <Image src={LOGO} alt=\"Logo\" />\r\n            <button\r\n              onClick={onClose}\r\n              className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\r\n            >\r\n              <X className=\"h-5 w-5 text-gray-500\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          <nav className=\"flex flex-col gap-4\">\r\n            {sidebarItems.map((item) => {\r\n              const isActive = pathname === item.href;\r\n              const Icon = item.icon;\r\n\r\n              return (\r\n                <Link\r\n                  key={item.label}\r\n                  href={item.href}\r\n                  className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\r\n                      ${\r\n                        isActive\r\n                          ? \"bg-purple-100 text-purple-700 font-extrabold\"\r\n                          : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"\r\n                      }`}\r\n                  onClick={onClose}\r\n                >\r\n                  <Icon\r\n                    className={`w-5 h-5 ${\r\n                      isActive ? \"text-purple-700\" : \"text-gray-400\"\r\n                    }`}\r\n                  />\r\n                  <span className=\"text-sm font-medium\">{item.label}</span>\r\n                </Link>\r\n              );\r\n            })}\r\n          </nav>\r\n        </aside>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAPA;;;;;;;AASA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,mOAAA,CAAA,oBAAiB;IACzB;CACD;AAED,MAAM,UAAU,CAAC,EACf,MAAM,EACN,OAAO,EAIR;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,qEAAqE;IACrE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,iBAAiB,OAAO,KAAK,YAAY,UAAU,SAAS;gBAC9D;YACF;YACA,iBAAiB,OAAO,GAAG;QAC7B;4BAAG;QAAC;QAAU;QAAQ;KAAQ;IAE9B,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,IACE,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,SAAS,SAAS,MAAM,MAAM,GAC9B;wBACA,IAAI,SAAS;oBACf;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;gBACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;4BAAG;QAAC;QAAQ;KAAQ;IAEpB,qBACE;;0BAEE,6LAAC;gBAAM,WAAU;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BAAC,KAAK,qSAAA,CAAA,UAAI;4BAAE,KAAI;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC;4BACjB,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;kBACV,EACE,WACI,iDACA,sDACJ;;kDAEJ,6LAAC;wCACC,WAAW,CAAC,QAAQ,EAClB,WAAW,oBAAoB,iBAC/B;;;;;;kDAEJ,6LAAC;wCAAK,WAAU;kDAAuB,KAAK,KAAK;;;;;;;+BAd5C,KAAK,KAAK;;;;;wBAiBrB;;;;;;;;;;;;0BAKJ,6LAAC;gBACC,IAAG;gBACH,WAAW,CAAC,0FAA0F,EACpG,SAAS,gBAAgB,iCACzB;0BAEF,cAAA,6LAAC;oBACC,IAAG;oBACH,WAAW,CAAC,gIAAgI,EAC1I,SAAS,kBAAkB,qBAC3B;;sCAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qSAAA,CAAA,UAAI;oCAAE,KAAI;;;;;;8CACtB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC;gCACjB,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,MAAM,OAAO,KAAK,IAAI;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC;sBACR,EACE,WACI,iDACA,sDACJ;oCACN,SAAS;;sDAET,6LAAC;4CACC,WAAW,CAAC,QAAQ,EAClB,WAAW,oBAAoB,iBAC/B;;;;;;sDAEJ,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAf5C,KAAK,KAAK;;;;;4BAkBrB;;;;;;;;;;;;;;;;;;;AAMZ;GA3IM;;QAOa,qIAAA,CAAA,cAAW;;;KAPxB;uCA6IS", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Navbar from \"@/components/navigation/navbar\";\r\nimport Sidebar from \"@/components/sideBar\";\r\nimport { ReactNode, useState, useCallback } from \"react\";\r\n\r\nconst RootLayout = ({ children }: { children: ReactNode }) => {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n\r\n  const toggleSidebar = useCallback(() => {\r\n    setIsSidebarOpen((prev) => !prev);\r\n  }, []);\r\n\r\n  const closeSidebar = useCallback(() => {\r\n    setIsSidebarOpen(false);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      {/* Sidebar */}\r\n      <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />\r\n\r\n      {/* Main content area */}\r\n      <div className=\"flex flex-col flex-1 overflow-hidden min-w-0\">\r\n        {/* Navbar */}\r\n        <Navbar onToggleSidebar={toggleSidebar} />\r\n\r\n        {/* Page content */}\r\n        <main className=\"flex-1 overflow-auto p-4 sm:p-6\">{children}</main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RootLayout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAA2B;;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC;yDAAiB,CAAC,OAAS,CAAC;;QAC9B;gDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC/B,iBAAiB;QACnB;+CAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yHAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAe,SAAS;;;;;;0BAGzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+IAAA,CAAA,UAAM;wBAAC,iBAAiB;;;;;;kCAGzB,6LAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;;AAI3D;GA1BM;KAAA;uCA4BS", "debugId": null}}]}