{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useTheme } from \"next-themes\"\r\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          \"--normal-bg\": \"var(--popover)\",\r\n          \"--normal-text\": \"var(--popover-foreground)\",\r\n          \"--normal-border\": \"var(--border)\",\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/Theme.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport {\r\n  ThemeProvider as NextThemeProvider,\r\n  ThemeProviderProps,\r\n} from \"next-themes\";\r\n\r\nconst ThemeProvider = ({ children, ...props }: ThemeProviderProps) => {\r\n  return <NextThemeProvider {...props}>{children}</NextThemeProvider>;\r\n};\r\n\r\nexport default ThemeProvider;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAOA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,GAAG,OAA2B;IAC/D,qBAAO,8OAAC,gJAAA,CAAA,gBAAiB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACxC;uCAEe", "debugId": null}}]}