"use client";
import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle, Clock } from "lucide-react";
import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import DIDAvatar from "@/components/DIDAvatar";
import InterviewLayout from "@/components/InterviewLayout";
import { useInterviewPreparation } from "@/hooks/useInterviewPreparation";
import InterviewPreparationOverlay from "@/components/InterviewPreparationOverlay";
import { motion } from "framer-motion";

type InterviewWithDIDProps = {
  onNext?: () => void;
  candidateName?: string;
  jobTitle?: string;
};

const InterviewWithDID: React.FC<InterviewWithDIDProps> = ({
  onNext,
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
}) => {
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);
  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);

  const questions = [
    `Hello {candidateName}, welcome to your interview for the {jobTitle} position. Let's start with our first question: Tell us about yourself and your background.`,
    "What are your key strengths and how do they relate to this insurance agent role?",
    "Why do you want to work as an insurance agent, and what interests you about this particular position?",
    "Where do you see yourself in 5 years, and how does this role fit into your career goals?",
    "Thank you for your responses. Do you have any questions about the role or our company before we conclude?"
  ];

  // Use the interview preparation hook
  const preparation = useInterviewPreparation({
    questions,
    candidateName,
    jobTitle,
    apiKey: process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "",
    autoStart: false, // We'll start manually when user clicks
  });

  const startInterview = async () => {
    setIsInterviewStarted(true);
    await preparation.startPreparation();
  };

  const handleVideoReady = () => {
    // Show submit button after a delay to allow the question to be spoken
    setTimeout(() => {
      setShowSubmitButton(true);
    }, 2000);
  };

  const handleVideoEnd = () => {
    // Video has finished playing, show submit button if not already shown
    if (!showSubmitButton) {
      setShowSubmitButton(true);
    }
  };

  const handleSubmitAnswer = async () => {
    if (!preparation.isLastQuestion) {
      setShowSubmitButton(false);

      // Move to next question
      const hasNext = await preparation.nextQuestion();
      if (!hasNext) {
        // Interview completed
        onNext?.();
      }
    } else {
      // Interview completed
      onNext?.();
    }
  };

  const isInterviewComplete = preparation.isLastQuestion && preparation.currentQuestionIndex >= questions.length;

  if (!isInterviewStarted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
            <QuestionsList className="h-[550px]" currentQuestion={0} />
            <div className="mt-6 md:mt-0">
              <DIDAvatar className="w-[300px] h-[300px]" />
            </div>
          </div>

          <div className="flex justify-center mt-10 gap-4">
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={startInterview}
            >
              Start Interview
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
          
          <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
            Ready to begin
          </div>
        </InterviewLayout>
      </div>
    );
  }

  if (isInterviewComplete) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-center mb-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Interview Completed!
              </h2>
              <p className="text-gray-600">
                Thank you for completing the interview. Your responses have been recorded.
              </p>
            </div>
            
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={() => onNext?.()}
            >
              View Results
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          </div>
        </InterviewLayout>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <JobInfoCard />
      <InterviewLayout>
        <div className="flex flex-col md:flex-row gap-10 justify-center items-center md:items-start">
          <QuestionsList className="h-[550px]" currentQuestion={preparation.currentQuestionIndex + 1} />
          <div className="mt-6 md:mt-0">
            <DIDAvatar
              className="w-[300px] h-[300px]"
              text={preparation.currentQuestionText}
              videoUrl={preparation.currentVideoUrl}
              onVideoReady={handleVideoReady}
              onVideoEnd={handleVideoEnd}
              isLoading={!preparation.isInterviewReady && isInterviewStarted}
              preloadingService={preparation.preloadingService || undefined}
              showPreloadingStatus={true}
              priority={1000 - preparation.currentQuestionIndex} // Higher priority for current questions
            />
          </div>
        </div>

        {/* Interview Preparation Overlay */}
        <InterviewPreparationOverlay
          isVisible={preparation.isPreparingInterview}
          progress={preparation.progress}
          currentStep={preparation.currentStep}
          totalQuestions={preparation.totalQuestions}
          loadedQuestions={preparation.loadedQuestions}
          failedQuestions={preparation.failedQuestions}
          estimatedTimeRemaining={preparation.estimatedTimeRemaining}
        />

        <div className="flex justify-center mt-10 gap-4">
          {showSubmitButton && preparation.isInterviewReady ? (
            <Button
              variant="default"
              className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
              onClick={handleSubmitAnswer}
            >
              {!preparation.isLastQuestion ? "Next Question" : "Finish Interview"}
              <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
            </Button>
          ) : (
            <div className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500">
              {!preparation.isInterviewReady ? (
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 animate-spin" />
                  {preparation.currentVideoUrl ? "Loading video..." : "Preparing question..."}
                </div>
              ) : (
                "Listen to the question"
              )}
            </div>
          )}
        </div>

        {/* <div className="flex justify-center mt-5 text-2xl font-semibold text-primary">
          Question {preparation.currentQuestionIndex + 1} of {questions.length}
        </div> */}
      </InterviewLayout>
    </div>
  );
};

export default InterviewWithDID;
