{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight } from \"lucide-react\";\nimport React, { useState } from \"react\";\n\ntype InterviewInstructionsProps = {\n  candidateName?: string;\n  jobTitle?: string;\n  languages?: string[];\n  instructions?: string[];\n  environmentChecklist?: string[];\n  disclaimers?: string[];\n  onNext?: () => void;\n};\n\nconst defaultInstructions = [\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n];\n\nconst defaultEnvironment = [\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n];\n\nconst defaultDisclaimers = [\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\n];\n\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\n  candidateName = \"Jonathan\",\n  jobTitle = \"Insurance Agent\",\n  languages = [\"English\", \"Chinese\"],\n  instructions = defaultInstructions,\n  environmentChecklist = defaultEnvironment,\n  disclaimers = defaultDisclaimers,\n  onNext,\n}) => {\n  const [isChecked, setIsChecked] = useState(false);\n\n  return (\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\n        <p className=\"font-semibold mb-8 text-xl\">\n          Instructions for Interview!\n        </p>\n        <div className=\"space-y-6\">\n          <div>\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\n            <p className=\"text-sm mb-4\">\n              As part of the process you are required to complete an AI video\n              assessment for the role of the {jobTitle}.\n            </p>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {languages.map((language, index) => (\n                <li key={index}>{language}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {instructions.map((instruction, index) => (\n                <li key={index}>{instruction}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {environmentChecklist.map((item, index) => (\n                <li key={index}>{item}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {disclaimers.map((disclaimer, index) => (\n                <li key={index}>{disclaimer}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div className=\"flex items-start gap-2 mt-6\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              checked={isChecked}\n              onChange={(e) => setIsChecked(e.target.checked)}\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n            />\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\n              By checking this box, you agree with AI Interview{\" \"}\n              <span className=\"text-primary cursor-pointer font-medium\">\n                Terms of use\n              </span>\n              .\n            </label>\n          </div>\n          <div className=\"flex justify-center\">\n            <Button\n              disabled={!isChecked}\n              variant=\"default\"\n              size=\"lg\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={() => onNext && onNext()}\n            >\n            Proceed\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InterviewInstructions;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,6LAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA9FM;KAAA;uCAgGS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAoC;sDAClC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["type QuestionsListProps = {\r\n  currentQuestion?: number;\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  currentQuestion = 1,\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,kBAAkB,CAAC,EACnB,SAAS,EACU;IACnB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,6LAAC;gCAAK,WAAU;;;;;;0CAElB,6LAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;KArDM;uCAuDS", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/images/avator.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 363, height: 663, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAICAYAAADeM14FAAAAk0lEQVR42gGIAHf/AIJ+cP6DgXL/oqaV/46Xdv4AfXhp/3hwZf+IiHb/j5J3/wB0c27/cmdh/399ef+oppj/AExbeP96gpf/UVtv/3l/if8ALDhQ/3qDlf9ka3r/bnB8/wBJU2v/jpWm/7m8xv/Evrz/AFhUW/+in6v/193o/9PZ4/8AYWRs/svT4P/a4+//ytbn/hi3VG7o7wRAAAAAAElFTkSuQmCC\", blurWidth: 4, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateImage.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport AvatarImage from \"@/public/images/avator.png\";\r\n\r\ntype CandidateImageProps = {\r\n  className?: string;\r\n};\r\n\r\nconst CandidateImage = ({ className }: CandidateImageProps) => {\r\n  return (\r\n    <div className=\"mt-6 md:mt-0\">\r\n      <Image\r\n        src={AvatarImage}\r\n        alt=\"Interviewee\"\r\n        className={`rounded-lg object-cover ${className}`}\r\n        width={300}\r\n        height={300}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateImage;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAuB;IACxD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK,uRAAA,CAAA,UAAW;YAChB,KAAI;YACJ,WAAW,CAAC,wBAAwB,EAAE,WAAW;YACjD,OAAO;YACP,QAAQ;;;;;;;;;;;AAIhB;KAZM;uCAcS", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\nimport { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuestionsPageProps = {\n  onNext?: () => void;\n};\n\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateImage />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            size=\"lg\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default QuestionsPage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/DIDAvatar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Bo<PERSON>, Loader2, Clock, CheckCircle, AlertCircle } from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { VideoPreloadingService } from \"@/lib/videoPreloadingService\";\n\nconst DID_API_URL = \"https://api.d-id.com/talks\";\n\ninterface DIDAvatarProps {\n  text?: string;\n  videoUrl?: string; // Pre-generated video URL\n  onVideoReady?: () => void;\n  onVideoEnd?: () => void;\n  className?: string;\n  isLoading?: boolean;\n  preloadingService?: VideoPreloadingService;\n  showPreloadingStatus?: boolean;\n  priority?: number; // Priority for video generation\n}\n\nconst DIDAvatar: React.FC<DIDAvatarProps> = ({\n  text,\n  videoUrl: preGeneratedVideoUrl,\n  onVideoReady,\n  onVideoEnd,\n  className = \"\",\n  isLoading = false,\n  preloadingService,\n  showPreloadingStatus = false,\n  priority = 0,\n}) => {\n  const [videoUrl, setVideoUrl] = useState<string | null>(preGeneratedVideoUrl || null);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [preloadingStatus, setPreloadingStatus] = useState<{\n    pending: number;\n    loading: number;\n    completed: number;\n    failed: number;\n    total: number;\n  } | null>(null);\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  const createTalk = async (inputText: string) => {\n    if (!inputText.trim()) return;\n\n    setLoading(true);\n    setVideoUrl(null);\n    setError(null);\n\n    try {\n      let videoUrlResponse: string | null = null;\n\n      // Try to use preloading service first\n      if (preloadingService) {\n        try {\n          videoUrlResponse = await preloadingService.getVideoUrl(inputText, priority);\n        } catch (err) {\n          console.warn(\"Failed to get video from preloading service, falling back to direct API:\", err);\n        }\n      }\n\n      // Fallback to direct API call if preloading service failed or not available\n      if (!videoUrlResponse) {\n        const payload = {\n          script: {\n            type: \"text\",\n            input: inputText.trim(),\n          },\n          // source_url: \"https://i.imgur.com/22232650.png\",//\n        };\n\n        const response = await fetch(DID_API_URL, {\n          method: \"POST\",\n          headers: {\n            Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\")}`,\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to create talk: ${response.statusText}`);\n        }\n\n        const data = await response.json();\n        const talkId = data.id;\n\n        // Poll for video completion\n        let attempts = 0;\n        const maxAttempts = 30; // 90 seconds max wait time\n\n        while (!videoUrlResponse && attempts < maxAttempts) {\n          const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {\n            headers: {\n              Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\")}`,\n              \"Content-Type\": \"application/json\",\n            },\n          });\n\n          const pollData = await pollResp.json();\n\n          if (pollData.status === \"done\" && pollData.result_url) {\n            videoUrlResponse = pollData.result_url;\n          } else if (pollData.status === \"error\") {\n            throw new Error(\"Video generation failed\");\n          } else {\n            await new Promise((res) => setTimeout(res, 3000));\n            attempts++;\n          }\n        }\n\n        if (!videoUrlResponse) {\n          throw new Error(\"Video generation timed out\");\n        }\n      }\n\n      setVideoUrl(videoUrlResponse);\n      onVideoReady?.();\n    } catch (err: any) {\n      console.error(\"D-ID API Error:\", err);\n      setError(err.message || \"Failed to generate video\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effect to handle preloading status updates\n  useEffect(() => {\n    if (preloadingService && showPreloadingStatus) {\n      const interval = setInterval(() => {\n        const status = preloadingService.getQueueStatus();\n        setPreloadingStatus(status);\n      }, 1000);\n\n      return () => clearInterval(interval);\n    }\n  }, [preloadingService, showPreloadingStatus]);\n\n  // Effect to handle pre-generated video URL\n  useEffect(() => {\n    if (preGeneratedVideoUrl) {\n      setVideoUrl(preGeneratedVideoUrl);\n      setLoading(false);\n      setError(null);\n      onVideoReady?.();\n    }\n  }, [preGeneratedVideoUrl, onVideoReady]);\n\n  // Effect to create video when text changes (only if no pre-generated URL)\n  useEffect(() => {\n    if (text && text.trim() && !preGeneratedVideoUrl) {\n      createTalk(text);\n    }\n  }, [text, preGeneratedVideoUrl]);\n\n  // Handle video end event\n  useEffect(() => {\n    const video = videoRef.current;\n    if (video) {\n      const handleVideoEnd = () => {\n        onVideoEnd?.();\n      };\n\n      video.addEventListener(\"ended\", handleVideoEnd);\n      return () => {\n        video.removeEventListener(\"ended\", handleVideoEnd);\n      };\n    }\n  }, [videoUrl, onVideoEnd]);\n\n  return (\n    <div className={`relative w-full h-full ${className}`}>\n      <AnimatePresence mode=\"wait\">\n        {videoUrl && !loading ? (\n          <motion.div\n            key=\"video\"\n            initial={{ scale: 0.95, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.95, opacity: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"w-full h-full relative\"\n          >\n            <video\n              ref={videoRef}\n              src={videoUrl}\n              autoPlay\n              className=\"w-full h-full object-cover rounded-lg\"\n              onLoadedData={() => onVideoReady?.()}\n            />\n            {/* Speaking indicator */}\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              className=\"absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2\"\n            >\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n              <span className=\"text-white text-sm font-medium\">Speaking</span>\n            </motion.div>\n\n            {/* Preloading status indicator */}\n            {showPreloadingStatus && preloadingStatus && preloadingStatus.total > 0 && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"absolute bottom-4 left-4 right-4 bg-black/40 backdrop-blur-sm rounded-lg px-3 py-2\"\n              >\n                <div className=\"flex items-center justify-between text-white text-xs\">\n                  <span>Preloading questions...</span>\n                  <span>{preloadingStatus.completed}/{preloadingStatus.total}</span>\n                </div>\n                <div className=\"w-full h-1 bg-white/20 rounded-full mt-1 overflow-hidden\">\n                  <motion.div\n                    className=\"h-full bg-gradient-to-r from-blue-400 to-green-400\"\n                    initial={{ width: 0 }}\n                    animate={{\n                      width: `${preloadingStatus.total > 0 ? (preloadingStatus.completed / preloadingStatus.total) * 100 : 0}%`\n                    }}\n                    transition={{ duration: 0.3 }}\n                  />\n                </div>\n              </motion.div>\n            )}\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"placeholder\"\n            initial={{ scale: 0.95, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.95, opacity: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative rounded-lg\"\n          >\n            {/* Large Bot Icon */}\n            <Bot className=\"w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8\" />\n\n            {/* Avatar Info */}\n            <div className=\"text-center text-white px-4\">\n              <h3 className=\"text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2\">\n                AI Interviewer\n              </h3>\n              <div className=\"flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2\">\n                <div className=\"w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-white font-medium text-sm lg:text-base\">\n                  {loading || isLoading ? \"Preparing...\" : \"Ready\"}\n                </span>\n              </div>\n            </div>\n\n            {/* Decorative elements */}\n            <div className=\"absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl\"></div>\n            <div className=\"absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl\"></div>\n            <div className=\"absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm\"></div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Loading Overlay */}\n      {(loading || isLoading) && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"absolute inset-0 bg-black/40 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg\"\n        >\n          <div className=\"bg-white/90 backdrop-blur-sm rounded-xl lg:rounded-2xl p-4 sm:p-6 lg:p-8 text-center shadow-2xl mx-4 max-w-sm\">\n            <Loader2 className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 animate-spin text-indigo-600 mx-auto mb-3 lg:mb-4\" />\n            <p className=\"text-sm sm:text-base text-gray-600 mb-4 lg:mb-6\">\n              Preparing your question...\n            </p>\n\n            {/* Progress bar */}\n            <div className=\"w-48 sm:w-56 lg:w-64 h-1.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden mx-auto\">\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600\"\n                initial={{ width: 0 }}\n                animate={{ width: \"100%\" }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n              />\n            </div>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"absolute inset-0 bg-red-50 flex flex-col items-center justify-center rounded-lg border-2 border-red-200\"\n        >\n          <div className=\"text-center text-red-600 px-4\">\n            <h3 className=\"text-lg font-semibold mb-2\">Error</h3>\n            <p className=\"text-sm\">{error}</p>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default DIDAvatar;\n"], "names": [], "mappings": ";;;AA2EyC;;AA1EzC;AACA;AAAA;AACA;AAAA;;;AAHA;;;;AAMA,MAAM,cAAc;AAcpB,MAAM,YAAsC,CAAC,EAC3C,IAAI,EACJ,UAAU,oBAAoB,EAC9B,YAAY,EACZ,UAAU,EACV,YAAY,EAAE,EACd,YAAY,KAAK,EACjB,iBAAiB,EACjB,uBAAuB,KAAK,EAC5B,WAAW,CAAC,EACb;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,wBAAwB;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAM7C;IACV,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,WAAW;QACX,YAAY;QACZ,SAAS;QAET,IAAI;YACF,IAAI,mBAAkC;YAEtC,sCAAsC;YACtC,IAAI,mBAAmB;gBACrB,IAAI;oBACF,mBAAmB,MAAM,kBAAkB,WAAW,CAAC,WAAW;gBACpE,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,4EAA4E;gBAC3F;YACF;YAEA,4EAA4E;YAC5E,IAAI,CAAC,kBAAkB;gBACrB,MAAM,UAAU;oBACd,QAAQ;wBACN,MAAM;wBACN,OAAO,UAAU,IAAI;oBACvB;gBAEF;gBAEA,MAAM,WAAW,MAAM,MAAM,aAAa;oBACxC,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,MAAM,EAAE,KAAK,0FAAuC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IAAI,KAAK;wBACpG,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;gBACjE;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,SAAS,KAAK,EAAE;gBAEtB,4BAA4B;gBAC5B,IAAI,WAAW;gBACf,MAAM,cAAc,IAAI,2BAA2B;gBAEnD,MAAO,CAAC,oBAAoB,WAAW,YAAa;oBAClD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,CAAC,EAAE,QAAQ,EAAE;wBACvD,SAAS;4BACP,eAAe,CAAC,MAAM,EAAE,KAAK,0FAAuC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IAAI,KAAK;4BACpG,gBAAgB;wBAClB;oBACF;oBAEA,MAAM,WAAW,MAAM,SAAS,IAAI;oBAEpC,IAAI,SAAS,MAAM,KAAK,UAAU,SAAS,UAAU,EAAE;wBACrD,mBAAmB,SAAS,UAAU;oBACxC,OAAO,IAAI,SAAS,MAAM,KAAK,SAAS;wBACtC,MAAM,IAAI,MAAM;oBAClB,OAAO;wBACL,MAAM,IAAI,QAAQ,CAAC,MAAQ,WAAW,KAAK;wBAC3C;oBACF;gBACF;gBAEA,IAAI,CAAC,kBAAkB;oBACrB,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,YAAY;YACZ;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,qBAAqB,sBAAsB;gBAC7C,MAAM,WAAW;oDAAY;wBAC3B,MAAM,SAAS,kBAAkB,cAAc;wBAC/C,oBAAoB;oBACtB;mDAAG;gBAEH;2CAAO,IAAM,cAAc;;YAC7B;QACF;8BAAG;QAAC;QAAmB;KAAqB;IAE5C,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,sBAAsB;gBACxB,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT;YACF;QACF;8BAAG;QAAC;QAAsB;KAAa;IAEvC,0EAA0E;IAC1E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,QAAQ,KAAK,IAAI,MAAM,CAAC,sBAAsB;gBAChD,WAAW;YACb;QACF;8BAAG;QAAC;QAAM;KAAqB;IAE/B,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,OAAO;gBACT,MAAM;0DAAiB;wBACrB;oBACF;;gBAEA,MAAM,gBAAgB,CAAC,SAAS;gBAChC;2CAAO;wBACL,MAAM,mBAAmB,CAAC,SAAS;oBACrC;;YACF;QACF;8BAAG;QAAC;QAAU;KAAW;IAEzB,qBACE,6LAAC;QAAI,WAAW,CAAC,uBAAuB,EAAE,WAAW;;0BACnD,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,YAAY,CAAC,wBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBACnC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BACC,KAAK;4BACL,KAAK;4BACL,QAAQ;4BACR,WAAU;4BACV,cAAc,IAAM;;;;;;sCAGtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;wBAIlD,wBAAwB,oBAAoB,iBAAiB,KAAK,GAAG,mBACpE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;gDAAM,iBAAiB,SAAS;gDAAC;gDAAE,iBAAiB,KAAK;;;;;;;;;;;;;8CAE5D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CACP,OAAO,GAAG,iBAAiB,KAAK,GAAG,IAAI,AAAC,iBAAiB,SAAS,GAAG,iBAAiB,KAAK,GAAI,MAAM,EAAE,CAAC,CAAC;wCAC3G;wCACA,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;;;;;;;mBA1ChC;;;;yCAiDN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBACnC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDACb,WAAW,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;sCAM/C,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;mBA1BX;;;;;;;;;;YAgCT,CAAC,WAAW,SAAS,mBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAY;;;;;;;;;;;;;;;;;;;;;;YAQxE,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAW;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GAvRM;KAAA;uCAyRS", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/videoPreloadingService.ts"], "sourcesContent": ["\"use client\";\n\n// Types\nexport interface VideoCache {\n  [key: string]: {\n    url: string;\n    timestamp: number;\n    expiresAt: number;\n  };\n}\n\nexport interface PreloadTask {\n  id: string;\n  text: string;\n  priority: number;\n  status: 'pending' | 'loading' | 'completed' | 'failed';\n  promise?: Promise<string>;\n  error?: string;\n}\n\nexport interface VideoPreloadingConfig {\n  apiKey: string;\n  sourceUrl?: string;\n  cacheExpiration?: number; // in milliseconds, default 1 hour\n  maxConcurrentRequests?: number;\n  retryAttempts?: number;\n}\n\n// D-ID API constants\nconst DID_API_URL = \"https://api.d-id.com/talks\";\nconst DEFAULT_CACHE_EXPIRATION = 60 * 60 * 1000; // 1 hour\nconst DEFAULT_MAX_CONCURRENT = 3;\nconst DEFAULT_RETRY_ATTEMPTS = 2;\n\nexport class DIDApiError extends Error {\n  constructor(message: string, public statusCode?: number) {\n    super(message);\n    this.name = 'DIDApiError';\n  }\n}\n\nexport class VideoPreloadingService {\n  private cache: VideoCache = {};\n  private preloadQueue: PreloadTask[] = [];\n  private activeRequests = 0;\n  private config: Required<VideoPreloadingConfig>;\n  private abortController: AbortController | null = null;\n\n  constructor(config: VideoPreloadingConfig) {\n    this.config = {\n      ...config,\n      cacheExpiration: config.cacheExpiration || DEFAULT_CACHE_EXPIRATION,\n      maxConcurrentRequests: config.maxConcurrentRequests || DEFAULT_MAX_CONCURRENT,\n      retryAttempts: config.retryAttempts || DEFAULT_RETRY_ATTEMPTS,\n      sourceUrl: config.sourceUrl ?? \"\", // Ensure sourceUrl is always a string\n    };\n    \n    // Load cache from localStorage if available\n    this.loadCacheFromStorage();\n    \n    // Clean expired cache entries\n    this.cleanExpiredCache();\n  }\n\n  private generateCacheKey(text: string): string {\n    // Simple hash function for cache key\n    let hash = 0;\n    for (let i = 0; i < text.length; i++) {\n      const char = text.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return `did_video_${Math.abs(hash)}`;\n  }\n\n  private loadCacheFromStorage(): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      const cached = localStorage.getItem('did_video_cache');\n      if (cached) {\n        this.cache = JSON.parse(cached);\n      }\n    } catch (error) {\n      console.warn('Failed to load video cache from storage:', error);\n      this.cache = {};\n    }\n  }\n\n  private saveCacheToStorage(): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      localStorage.setItem('did_video_cache', JSON.stringify(this.cache));\n    } catch (error) {\n      console.warn('Failed to save video cache to storage:', error);\n    }\n  }\n\n  private cleanExpiredCache(): void {\n    const now = Date.now();\n    let hasExpired = false;\n    \n    for (const key in this.cache) {\n      if (this.cache[key].expiresAt < now) {\n        delete this.cache[key];\n        hasExpired = true;\n      }\n    }\n    \n    if (hasExpired) {\n      this.saveCacheToStorage();\n    }\n  }\n\n  private async generateVideo(text: string, retryCount = 0): Promise<string> {\n    const payload = {\n      script: {\n        type: \"text\",\n        input: text.trim(),\n      },\n      ...(this.config.sourceUrl && { source_url: this.config.sourceUrl }),\n    };\n\n    try {\n      // Create talk\n      const response = await fetch(DID_API_URL, {\n        method: \"POST\",\n        headers: {\n          Authorization: `Basic ${btoa(this.config.apiKey)}`,\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(payload),\n        signal: this.abortController?.signal,\n      });\n\n      if (!response.ok) {\n        throw new DIDApiError(`Failed to create talk: ${response.statusText}`, response.status);\n      }\n\n      const data = await response.json();\n      const talkId = data.id;\n\n      // Poll for completion\n      let attempts = 0;\n      const maxAttempts = 30; // 90 seconds max wait time\n\n      while (attempts < maxAttempts) {\n        if (this.abortController?.signal.aborted) {\n          throw new DIDApiError('Request aborted');\n        }\n\n        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {\n          headers: {\n            Authorization: `Basic ${btoa(this.config.apiKey)}`,\n            \"Content-Type\": \"application/json\",\n          },\n          signal: this.abortController?.signal,\n        });\n\n        const pollData = await pollResp.json();\n\n        if (pollData.status === \"done\" && pollData.result_url) {\n          return pollData.result_url;\n        } else if (pollData.status === \"error\") {\n          throw new DIDApiError(\"Video generation failed\");\n        }\n\n        await new Promise((resolve) => setTimeout(resolve, 3000));\n        attempts++;\n      }\n\n      throw new DIDApiError(\"Video generation timed out\");\n    } catch (error) {\n      // Retry logic\n      if (retryCount < this.config.retryAttempts && !(error instanceof DIDApiError && error.statusCode === 401)) {\n        console.warn(`Retrying video generation (attempt ${retryCount + 1}):`, error);\n        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff\n        return this.generateVideo(text, retryCount + 1);\n      }\n\n      console.error(\"D-ID API Error:\", error);\n      throw error instanceof DIDApiError ? error : new DIDApiError(\n        error instanceof Error ? error.message : \"Unknown error generating video\"\n      );\n    }\n  }\n\n  private async processQueue(): Promise<void> {\n    while (this.preloadQueue.length > 0 && this.activeRequests < this.config.maxConcurrentRequests) {\n      // Find highest priority pending task\n      const taskIndex = this.preloadQueue.findIndex(task => task.status === 'pending');\n      if (taskIndex === -1) break;\n\n      const task = this.preloadQueue[taskIndex];\n      task.status = 'loading';\n      this.activeRequests++;\n\n      try {\n        const videoUrl = await this.generateVideo(task.text);\n        \n        // Cache the result\n        const cacheKey = this.generateCacheKey(task.text);\n        const now = Date.now();\n        this.cache[cacheKey] = {\n          url: videoUrl,\n          timestamp: now,\n          expiresAt: now + this.config.cacheExpiration,\n        };\n        this.saveCacheToStorage();\n\n        task.status = 'completed';\n        \n        // Remove completed task from queue\n        this.preloadQueue.splice(taskIndex, 1);\n        \n      } catch (error) {\n        task.status = 'failed';\n        task.error = error instanceof Error ? error.message : 'Unknown error';\n        \n        // Remove failed task from queue\n        this.preloadQueue.splice(taskIndex, 1);\n      } finally {\n        this.activeRequests--;\n      }\n    }\n  }\n\n  public async getVideoUrl(text: string, priority: number = 0): Promise<string> {\n    if (!text.trim()) {\n      throw new DIDApiError('Question text is required');\n    }\n\n    const cacheKey = this.generateCacheKey(text);\n    \n    // Check cache first\n    const cached = this.cache[cacheKey];\n    if (cached && cached.expiresAt > Date.now()) {\n      return cached.url;\n    }\n\n    // Check if already in queue or being processed\n    const existingTask = this.preloadQueue.find(task => task.text === text);\n    if (existingTask) {\n      if (existingTask.promise) {\n        return existingTask.promise;\n      }\n      // Update priority if higher\n      if (priority > existingTask.priority) {\n        existingTask.priority = priority;\n        // Re-sort queue by priority\n        this.preloadQueue.sort((a, b) => b.priority - a.priority);\n      }\n    } else {\n      // Add to queue\n      const task: PreloadTask = {\n        id: `task_${Date.now()}_${Math.random()}`,\n        text,\n        priority,\n        status: 'pending',\n      };\n      \n      // Create promise for this task\n      task.promise = new Promise((resolve, reject) => {\n        const checkTask = () => {\n          const currentTask = this.preloadQueue.find(t => t.id === task.id);\n          if (!currentTask) {\n            // Task completed, check cache\n            const cached = this.cache[cacheKey];\n            if (cached) {\n              resolve(cached.url);\n            } else {\n              reject(new Error('Task completed but no cached result found'));\n            }\n          } else if (currentTask.status === 'failed') {\n            reject(new Error(currentTask.error || 'Video generation failed'));\n          } else {\n            // Still processing, check again\n            setTimeout(checkTask, 500);\n          }\n        };\n        \n        setTimeout(checkTask, 100);\n      });\n      \n      this.preloadQueue.push(task);\n      // Sort by priority (highest first)\n      this.preloadQueue.sort((a, b) => b.priority - a.priority);\n    }\n\n    // Start processing queue\n    this.processQueue();\n\n    // Return promise for this specific task\n    return existingTask?.promise || this.preloadQueue.find(t => t.text === text)?.promise || Promise.reject(new Error('Failed to create task'));\n  }\n\n  public preloadVideos(texts: string[], basePriority: number = 0): void {\n    texts.forEach((text, index) => {\n      const priority = basePriority - index; // Earlier questions get higher priority\n      this.getVideoUrl(text, priority).catch(error => {\n        console.warn(`Failed to preload video for: \"${text.substring(0, 50)}...\"`, error);\n      });\n    });\n  }\n\n  public getQueueStatus(): {\n    pending: number;\n    loading: number;\n    completed: number;\n    failed: number;\n    total: number;\n  } {\n    const pending = this.preloadQueue.filter(t => t.status === 'pending').length;\n    const loading = this.preloadQueue.filter(t => t.status === 'loading').length;\n    const completed = this.preloadQueue.filter(t => t.status === 'completed').length;\n    const failed = this.preloadQueue.filter(t => t.status === 'failed').length;\n    \n    return {\n      pending,\n      loading,\n      completed,\n      failed,\n      total: this.preloadQueue.length,\n    };\n  }\n\n  public clearCache(): void {\n    this.cache = {};\n    this.saveCacheToStorage();\n  }\n\n  public abort(): void {\n    if (this.abortController) {\n      this.abortController.abort();\n    }\n    this.abortController = new AbortController();\n    \n    // Clear queue\n    this.preloadQueue = [];\n    this.activeRequests = 0;\n  }\n\n  public destroy(): void {\n    this.abort();\n    this.clearCache();\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AA4BA,qBAAqB;AACrB,MAAM,cAAc;AACpB,MAAM,2BAA2B,KAAK,KAAK,MAAM,SAAS;AAC1D,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB;AAExB,MAAM,oBAAoB;;IAC/B,YAAY,OAAe,EAAE,AAAO,UAAmB,CAAE;QACvD,KAAK,CAAC,eAD4B,aAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM;IACH,QAAoB,CAAC,EAAE;IACvB,eAA8B,EAAE,CAAC;IACjC,iBAAiB,EAAE;IACnB,OAAwC;IACxC,kBAA0C,KAAK;IAEvD,YAAY,MAA6B,CAAE;QACzC,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,MAAM;YACT,iBAAiB,OAAO,eAAe,IAAI;YAC3C,uBAAuB,OAAO,qBAAqB,IAAI;YACvD,eAAe,OAAO,aAAa,IAAI;YACvC,WAAW,OAAO,SAAS,IAAI;QACjC;QAEA,4CAA4C;QAC5C,IAAI,CAAC,oBAAoB;QAEzB,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB;IACxB;IAEQ,iBAAiB,IAAY,EAAU;QAC7C,qCAAqC;QACrC,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,KAAK,UAAU,CAAC;YAC7B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;YAC9B,OAAO,OAAO,MAAM,4BAA4B;QAClD;QACA,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAAC,OAAO;IACtC;IAEQ,uBAA6B;QACnC,uCAAmC;;QAAM;QAEzC,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,4CAA4C;YACzD,IAAI,CAAC,KAAK,GAAG,CAAC;QAChB;IACF;IAEQ,qBAA2B;QACjC,uCAAmC;;QAAM;QAEzC,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,IAAI,CAAC,KAAK;QACnE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,0CAA0C;QACzD;IACF;IAEQ,oBAA0B;QAChC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,aAAa;QAEjB,IAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAE;YAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK;gBACnC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,YAAY;YACd,IAAI,CAAC,kBAAkB;QACzB;IACF;IAEA,MAAc,cAAc,IAAY,EAAE,aAAa,CAAC,EAAmB;QACzE,MAAM,UAAU;YACd,QAAQ;gBACN,MAAM;gBACN,OAAO,KAAK,IAAI;YAClB;YACA,GAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI;gBAAE,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;YAAC,CAAC;QACpE;QAEA,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;oBAClD,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,IAAI,CAAC,eAAe,EAAE;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,YAAY,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE,EAAE,SAAS,MAAM;YACxF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,SAAS,KAAK,EAAE;YAEtB,sBAAsB;YACtB,IAAI,WAAW;YACf,MAAM,cAAc,IAAI,2BAA2B;YAEnD,MAAO,WAAW,YAAa;gBAC7B,IAAI,IAAI,CAAC,eAAe,EAAE,OAAO,SAAS;oBACxC,MAAM,IAAI,YAAY;gBACxB;gBAEA,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,CAAC,EAAE,QAAQ,EAAE;oBACvD,SAAS;wBACP,eAAe,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;wBAClD,gBAAgB;oBAClB;oBACA,QAAQ,IAAI,CAAC,eAAe,EAAE;gBAChC;gBAEA,MAAM,WAAW,MAAM,SAAS,IAAI;gBAEpC,IAAI,SAAS,MAAM,KAAK,UAAU,SAAS,UAAU,EAAE;oBACrD,OAAO,SAAS,UAAU;gBAC5B,OAAO,IAAI,SAAS,MAAM,KAAK,SAAS;oBACtC,MAAM,IAAI,YAAY;gBACxB;gBAEA,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBACnD;YACF;YAEA,MAAM,IAAI,YAAY;QACxB,EAAE,OAAO,OAAO;YACd,cAAc;YACd,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,iBAAiB,eAAe,MAAM,UAAU,KAAK,GAAG,GAAG;gBACzG,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE;gBACvE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,sBAAsB;gBAClG,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,aAAa;YAC/C;YAEA,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,iBAAiB,cAAc,QAAQ,IAAI,YAC/C,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF;IAEA,MAAc,eAA8B;QAC1C,MAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAE;YAC9F,qCAAqC;YACrC,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YACtE,IAAI,cAAc,CAAC,GAAG;YAEtB,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;YACzC,KAAK,MAAM,GAAG;YACd,IAAI,CAAC,cAAc;YAEnB,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI;gBAEnD,mBAAmB;gBACnB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;gBAChD,MAAM,MAAM,KAAK,GAAG;gBACpB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;oBACrB,KAAK;oBACL,WAAW;oBACX,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe;gBAC9C;gBACA,IAAI,CAAC,kBAAkB;gBAEvB,KAAK,MAAM,GAAG;gBAEd,mCAAmC;gBACnC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW;YAEtC,EAAE,OAAO,OAAO;gBACd,KAAK,MAAM,GAAG;gBACd,KAAK,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAEtD,gCAAgC;gBAChC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW;YACtC,SAAU;gBACR,IAAI,CAAC,cAAc;YACrB;QACF;IACF;IAEA,MAAa,YAAY,IAAY,EAAE,WAAmB,CAAC,EAAmB;QAC5E,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,MAAM,IAAI,YAAY;QACxB;QAEA,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;QAEvC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;QACnC,IAAI,UAAU,OAAO,SAAS,GAAG,KAAK,GAAG,IAAI;YAC3C,OAAO,OAAO,GAAG;QACnB;QAEA,+CAA+C;QAC/C,MAAM,eAAe,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAClE,IAAI,cAAc;YAChB,IAAI,aAAa,OAAO,EAAE;gBACxB,OAAO,aAAa,OAAO;YAC7B;YACA,4BAA4B;YAC5B,IAAI,WAAW,aAAa,QAAQ,EAAE;gBACpC,aAAa,QAAQ,GAAG;gBACxB,4BAA4B;gBAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAC1D;QACF,OAAO;YACL,eAAe;YACf,MAAM,OAAoB;gBACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI;gBACzC;gBACA;gBACA,QAAQ;YACV;YAEA,+BAA+B;YAC/B,KAAK,OAAO,GAAG,IAAI,QAAQ,CAAC,SAAS;gBACnC,MAAM,YAAY;oBAChB,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;oBAChE,IAAI,CAAC,aAAa;wBAChB,8BAA8B;wBAC9B,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;wBACnC,IAAI,QAAQ;4BACV,QAAQ,OAAO,GAAG;wBACpB,OAAO;4BACL,OAAO,IAAI,MAAM;wBACnB;oBACF,OAAO,IAAI,YAAY,MAAM,KAAK,UAAU;wBAC1C,OAAO,IAAI,MAAM,YAAY,KAAK,IAAI;oBACxC,OAAO;wBACL,gCAAgC;wBAChC,WAAW,WAAW;oBACxB;gBACF;gBAEA,WAAW,WAAW;YACxB;YAEA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB,mCAAmC;YACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAC1D;QAEA,yBAAyB;QACzB,IAAI,CAAC,YAAY;QAEjB,wCAAwC;QACxC,OAAO,cAAc,WAAW,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,WAAW,QAAQ,MAAM,CAAC,IAAI,MAAM;IACpH;IAEO,cAAc,KAAe,EAAE,eAAuB,CAAC,EAAQ;QACpE,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,WAAW,eAAe,OAAO,wCAAwC;YAC/E,IAAI,CAAC,WAAW,CAAC,MAAM,UAAU,KAAK,CAAC,CAAA;gBACrC,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,KAAK,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;YAC7E;QACF;IACF;IAEO,iBAML;QACA,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC5E,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC5E,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAChF,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QAE1E,OAAO;YACL;YACA;YACA;YACA;YACA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;IAEO,aAAmB;QACxB,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,kBAAkB;IACzB;IAEO,QAAc;QACnB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK;QAC5B;QACA,IAAI,CAAC,eAAe,GAAG,IAAI;QAE3B,cAAc;QACd,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,cAAc,GAAG;IACxB;IAEO,UAAgB;QACrB,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,UAAU;IACjB;AACF", "debugId": null}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/hooks/useInterviewPreparation.ts"], "sourcesContent": ["\"use client\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { VideoPreloadingService } from \"@/lib/videoPreloadingService\";\n\ninterface PreparationStatus {\n  isPreparingInterview: boolean;\n  progress: number;\n  currentStep: string;\n  totalQuestions: number;\n  loadedQuestions: number;\n  failedQuestions: number;\n  estimatedTimeRemaining: number;\n  error: string | null;\n}\n\ninterface UseInterviewPreparationOptions {\n  questions: string[];\n  candidateName?: string;\n  jobTitle?: string;\n  apiKey?: string;\n  sourceUrl?: string;\n  autoStart?: boolean;\n}\n\nexport const useInterviewPreparation = (options: UseInterviewPreparationOptions) => {\n  const {\n    questions,\n    candidateName = \"Candidate\",\n    jobTitle = \"Position\",\n    apiKey,\n    sourceUrl,\n    autoStart = false,\n  } = options;\n\n  const [status, setStatus] = useState<PreparationStatus>({\n    isPreparingInterview: false,\n    progress: 0,\n    currentStep: \"\",\n    totalQuestions: questions.length,\n    loadedQuestions: 0,\n    failedQuestions: 0,\n    estimatedTimeRemaining: 0,\n    error: null,\n  });\n\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [currentVideoUrl, setCurrentVideoUrl] = useState<string>(\"\");\n  const [isInterviewReady, setIsInterviewReady] = useState(false);\n\n  const preloadingServiceRef = useRef<VideoPreloadingService | null>(null);\n  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  const startTimeRef = useRef<number>(0);\n\n  // Personalize questions with candidate name and job title\n  const personalizedQuestions = questions.map((question, index) => {\n    if (index === 0) {\n      return question\n        .replace(/\\{candidateName\\}/g, candidateName)\n        .replace(/\\{jobTitle\\}/g, jobTitle);\n    }\n    return question;\n  });\n\n  const updateStatus = useCallback((updates: Partial<PreparationStatus>) => {\n    setStatus(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  const estimateTimeRemaining = useCallback((completed: number, total: number, startTime: number): number => {\n    if (completed === 0) return 0;\n    \n    const elapsed = Date.now() - startTime;\n    const avgTimePerQuestion = elapsed / completed;\n    const remaining = total - completed;\n    \n    return Math.ceil((remaining * avgTimePerQuestion) / 1000);\n  }, []);\n\n  const startPreparation = useCallback(async () => {\n    if (!apiKey) {\n      updateStatus({ \n        error: \"API key is required for video generation\",\n        isPreparingInterview: false \n      });\n      return;\n    }\n\n    updateStatus({\n      isPreparingInterview: true,\n      progress: 0,\n      currentStep: \"Initializing interview preparation...\",\n      error: null,\n    });\n\n    startTimeRef.current = Date.now();\n\n    try {\n      // Initialize preloading service\n      preloadingServiceRef.current = new VideoPreloadingService({\n        apiKey,\n        sourceUrl,\n        cacheExpiration: 60 * 60 * 1000, // 1 hour\n        maxConcurrentRequests: 3,\n        retryAttempts: 2,\n      });\n\n      updateStatus({ currentStep: \"Starting background video generation...\" });\n\n      // Start preloading all questions\n      preloadingServiceRef.current.preloadVideos(personalizedQuestions, 100);\n\n      // Monitor progress\n      progressIntervalRef.current = setInterval(() => {\n        if (preloadingServiceRef.current) {\n          const queueStatus = preloadingServiceRef.current.getQueueStatus();\n          const progress = queueStatus.total > 0 ? \n            (queueStatus.completed / queueStatus.total) * 100 : 0;\n          \n          const estimatedTime = estimateTimeRemaining(\n            queueStatus.completed,\n            queueStatus.total,\n            startTimeRef.current\n          );\n\n          updateStatus({\n            progress,\n            loadedQuestions: queueStatus.completed,\n            failedQuestions: queueStatus.failed,\n            estimatedTimeRemaining: estimatedTime,\n            currentStep: queueStatus.loading > 0 ? \n              `Generating video ${queueStatus.completed + 1} of ${queueStatus.total}...` :\n              queueStatus.completed === queueStatus.total ?\n                \"All questions ready!\" :\n                \"Preparing questions...\",\n          });\n\n          // Check if preparation is complete\n          if (queueStatus.completed + queueStatus.failed >= queueStatus.total) {\n            if (progressIntervalRef.current) {\n              clearInterval(progressIntervalRef.current);\n              progressIntervalRef.current = null;\n            }\n\n            // Load first question\n            loadQuestion(0);\n            \n            updateStatus({\n              isPreparingInterview: false,\n              currentStep: \"Interview ready to start!\",\n            });\n            \n            setIsInterviewReady(true);\n          }\n        }\n      }, 500);\n\n    } catch (error) {\n      console.error(\"Failed to start interview preparation:\", error);\n      updateStatus({\n        error: error instanceof Error ? error.message : \"Failed to prepare interview\",\n        isPreparingInterview: false,\n      });\n    }\n  }, [apiKey, sourceUrl, personalizedQuestions, updateStatus, estimateTimeRemaining]);\n\n  const loadQuestion = useCallback(async (questionIndex: number) => {\n    if (!preloadingServiceRef.current || questionIndex >= personalizedQuestions.length) {\n      return;\n    }\n\n    const questionText = personalizedQuestions[questionIndex];\n    \n    try {\n      const videoUrl = await preloadingServiceRef.current.getVideoUrl(\n        questionText, \n        1000 - questionIndex // Higher priority for earlier questions\n      );\n      \n      setCurrentVideoUrl(videoUrl);\n      setCurrentQuestionIndex(questionIndex);\n    } catch (error) {\n      console.error(`Failed to load question ${questionIndex}:`, error);\n      setCurrentVideoUrl(\"\");\n    }\n  }, [personalizedQuestions]);\n\n  const nextQuestion = useCallback(async () => {\n    const nextIndex = currentQuestionIndex + 1;\n    if (nextIndex < personalizedQuestions.length) {\n      await loadQuestion(nextIndex);\n      return true;\n    }\n    return false; // Interview completed\n  }, [currentQuestionIndex, personalizedQuestions.length, loadQuestion]);\n\n  const getCurrentQuestion = useCallback(() => {\n    return personalizedQuestions[currentQuestionIndex] || \"\";\n  }, [personalizedQuestions, currentQuestionIndex]);\n\n  const getProgress = useCallback(() => {\n    return personalizedQuestions.length > 0 ? \n      ((currentQuestionIndex + 1) / personalizedQuestions.length) * 100 : 0;\n  }, [personalizedQuestions.length, currentQuestionIndex]);\n\n  // Auto-start preparation if enabled\n  useEffect(() => {\n    if (autoStart && !status.isPreparingInterview && !isInterviewReady) {\n      startPreparation();\n    }\n  }, [autoStart, status.isPreparingInterview, isInterviewReady, startPreparation]);\n\n  // Cleanup\n  useEffect(() => {\n    return () => {\n      if (progressIntervalRef.current) {\n        clearInterval(progressIntervalRef.current);\n      }\n      if (preloadingServiceRef.current) {\n        preloadingServiceRef.current.destroy();\n      }\n    };\n  }, []);\n\n  return {\n    // Status\n    ...status,\n    isInterviewReady,\n    \n    // Current question data\n    currentQuestionIndex,\n    currentVideoUrl,\n    currentQuestionText: getCurrentQuestion(),\n    interviewProgress: getProgress(),\n    \n    // Actions\n    startPreparation,\n    nextQuestion,\n    loadQuestion,\n    \n    // Utilities\n    preloadingService: preloadingServiceRef.current,\n    personalizedQuestions,\n    isLastQuestion: currentQuestionIndex >= personalizedQuestions.length - 1,\n  };\n};\n"], "names": [], "mappings": ";;;AACA;AACA;;AAFA;;;AAwBO,MAAM,0BAA0B,CAAC;;IACtC,MAAM,EACJ,SAAS,EACT,gBAAgB,WAAW,EAC3B,WAAW,UAAU,EACrB,MAAM,EACN,SAAS,EACT,YAAY,KAAK,EAClB,GAAG;IAEJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QACtD,sBAAsB;QACtB,UAAU;QACV,aAAa;QACb,gBAAgB,UAAU,MAAM;QAChC,iBAAiB;QACjB,iBAAiB;QACjB,wBAAwB;QACxB,OAAO;IACT;IAEA,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC;IACnE,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAEpC,0DAA0D;IAC1D,MAAM,wBAAwB,UAAU,GAAG,CAAC,CAAC,UAAU;QACrD,IAAI,UAAU,GAAG;YACf,OAAO,SACJ,OAAO,CAAC,sBAAsB,eAC9B,OAAO,CAAC,iBAAiB;QAC9B;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YAChC;qEAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,OAAO;oBAAC,CAAC;;QAC5C;4DAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sEAAE,CAAC,WAAmB,OAAe;YAC3E,IAAI,cAAc,GAAG,OAAO;YAE5B,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAM,qBAAqB,UAAU;YACrC,MAAM,YAAY,QAAQ;YAE1B,OAAO,KAAK,IAAI,CAAC,AAAC,YAAY,qBAAsB;QACtD;qEAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACnC,IAAI,CAAC,QAAQ;gBACX,aAAa;oBACX,OAAO;oBACP,sBAAsB;gBACxB;gBACA;YACF;YAEA,aAAa;gBACX,sBAAsB;gBACtB,UAAU;gBACV,aAAa;gBACb,OAAO;YACT;YAEA,aAAa,OAAO,GAAG,KAAK,GAAG;YAE/B,IAAI;gBACF,gCAAgC;gBAChC,qBAAqB,OAAO,GAAG,IAAI,gIAAA,CAAA,yBAAsB,CAAC;oBACxD;oBACA;oBACA,iBAAiB,KAAK,KAAK;oBAC3B,uBAAuB;oBACvB,eAAe;gBACjB;gBAEA,aAAa;oBAAE,aAAa;gBAA0C;gBAEtE,iCAAiC;gBACjC,qBAAqB,OAAO,CAAC,aAAa,CAAC,uBAAuB;gBAElE,mBAAmB;gBACnB,oBAAoB,OAAO,GAAG;6EAAY;wBACxC,IAAI,qBAAqB,OAAO,EAAE;4BAChC,MAAM,cAAc,qBAAqB,OAAO,CAAC,cAAc;4BAC/D,MAAM,WAAW,YAAY,KAAK,GAAG,IACnC,AAAC,YAAY,SAAS,GAAG,YAAY,KAAK,GAAI,MAAM;4BAEtD,MAAM,gBAAgB,sBACpB,YAAY,SAAS,EACrB,YAAY,KAAK,EACjB,aAAa,OAAO;4BAGtB,aAAa;gCACX;gCACA,iBAAiB,YAAY,SAAS;gCACtC,iBAAiB,YAAY,MAAM;gCACnC,wBAAwB;gCACxB,aAAa,YAAY,OAAO,GAAG,IACjC,CAAC,iBAAiB,EAAE,YAAY,SAAS,GAAG,EAAE,IAAI,EAAE,YAAY,KAAK,CAAC,GAAG,CAAC,GAC1E,YAAY,SAAS,KAAK,YAAY,KAAK,GACzC,yBACA;4BACN;4BAEA,mCAAmC;4BACnC,IAAI,YAAY,SAAS,GAAG,YAAY,MAAM,IAAI,YAAY,KAAK,EAAE;gCACnE,IAAI,oBAAoB,OAAO,EAAE;oCAC/B,cAAc,oBAAoB,OAAO;oCACzC,oBAAoB,OAAO,GAAG;gCAChC;gCAEA,sBAAsB;gCACtB,aAAa;gCAEb,aAAa;oCACX,sBAAsB;oCACtB,aAAa;gCACf;gCAEA,oBAAoB;4BACtB;wBACF;oBACF;4EAAG;YAEL,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,aAAa;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,sBAAsB;gBACxB;YACF;QACF;gEAAG;QAAC;QAAQ;QAAW;QAAuB;QAAc;KAAsB;IAElF,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YACtC,IAAI,CAAC,qBAAqB,OAAO,IAAI,iBAAiB,sBAAsB,MAAM,EAAE;gBAClF;YACF;YAEA,MAAM,eAAe,qBAAqB,CAAC,cAAc;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,qBAAqB,OAAO,CAAC,WAAW,CAC7D,cACA,OAAO,cAAc,wCAAwC;;gBAG/D,mBAAmB;gBACnB,wBAAwB;YAC1B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,EAAE;gBAC3D,mBAAmB;YACrB;QACF;4DAAG;QAAC;KAAsB;IAE1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAC/B,MAAM,YAAY,uBAAuB;YACzC,IAAI,YAAY,sBAAsB,MAAM,EAAE;gBAC5C,MAAM,aAAa;gBACnB,OAAO;YACT;YACA,OAAO,OAAO,sBAAsB;QACtC;4DAAG;QAAC;QAAsB,sBAAsB,MAAM;QAAE;KAAa;IAErE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAAE;YACrC,OAAO,qBAAqB,CAAC,qBAAqB,IAAI;QACxD;kEAAG;QAAC;QAAuB;KAAqB;IAEhD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC9B,OAAO,sBAAsB,MAAM,GAAG,IACpC,AAAC,CAAC,uBAAuB,CAAC,IAAI,sBAAsB,MAAM,GAAI,MAAM;QACxE;2DAAG;QAAC,sBAAsB,MAAM;QAAE;KAAqB;IAEvD,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,aAAa,CAAC,OAAO,oBAAoB,IAAI,CAAC,kBAAkB;gBAClE;YACF;QACF;4CAAG;QAAC;QAAW,OAAO,oBAAoB;QAAE;QAAkB;KAAiB;IAE/E,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;qDAAO;oBACL,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,cAAc,oBAAoB,OAAO;oBAC3C;oBACA,IAAI,qBAAqB,OAAO,EAAE;wBAChC,qBAAqB,OAAO,CAAC,OAAO;oBACtC;gBACF;;QACF;4CAAG,EAAE;IAEL,OAAO;QACL,SAAS;QACT,GAAG,MAAM;QACT;QAEA,wBAAwB;QACxB;QACA;QACA,qBAAqB;QACrB,mBAAmB;QAEnB,UAAU;QACV;QACA;QACA;QAEA,YAAY;QACZ,mBAAmB,qBAAqB,OAAO;QAC/C;QACA,gBAAgB,wBAAwB,sBAAsB,MAAM,GAAG;IACzE;AACF;GA3Na", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewWithDID.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, CheckCircle, Clock } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport DIDAvatar from \"@/components/DIDAvatar\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { useInterviewPreparation } from \"@/hooks/useInterviewPreparation\";\nimport InterviewPreparationOverlay from \"@/components/InterviewPreparationOverlay\";\nimport { motion } from \"framer-motion\";\n\ntype InterviewWithDIDProps = {\n  onNext?: () => void;\n  candidateName?: string;\n  jobTitle?: string;\n};\n\nconst InterviewWithDID: React.FC<InterviewWithDIDProps> = ({\n  onNext,\n  candidateName = \"Jonathan\",\n  jobTitle = \"Insurance Agent\",\n}) => {\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\n  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);\n\n  const questions = [\n    `Hello {candidateName}, welcome to your interview for the {jobTitle} position. Let's start with our first question: Tell us about yourself and your background.`,\n    \"What are your key strengths and how do they relate to this insurance agent role?\",\n    \"Why do you want to work as an insurance agent, and what interests you about this particular position?\",\n    \"Where do you see yourself in 5 years, and how does this role fit into your career goals?\",\n    \"Thank you for your responses. Do you have any questions about the role or our company before we conclude?\"\n  ];\n\n  // Use the interview preparation hook\n  const preparation = useInterviewPreparation({\n    questions,\n    candidateName,\n    jobTitle,\n    apiKey: process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\",\n    autoStart: false, // We'll start manually when user clicks\n  });\n\n  const startInterview = async () => {\n    setIsInterviewStarted(true);\n    await preparation.startPreparation();\n  };\n\n  const handleVideoReady = () => {\n    // Show submit button after a delay to allow the question to be spoken\n    setTimeout(() => {\n      setShowSubmitButton(true);\n    }, 2000);\n  };\n\n  const handleVideoEnd = () => {\n    // Video has finished playing, show submit button if not already shown\n    if (!showSubmitButton) {\n      setShowSubmitButton(true);\n    }\n  };\n\n  const handleSubmitAnswer = async () => {\n    if (!preparation.isLastQuestion) {\n      setShowSubmitButton(false);\n\n      // Move to next question\n      const hasNext = await preparation.nextQuestion();\n      if (!hasNext) {\n        // Interview completed\n        onNext?.();\n      }\n    } else {\n      // Interview completed\n      onNext?.();\n    }\n  };\n\n  const isInterviewComplete = preparation.isLastQuestion && preparation.currentQuestionIndex >= questions.length;\n\n  if (!isInterviewStarted) {\n    return (\n      <div className=\"h-screen\">\n        <JobInfoCard />\n        <InterviewLayout>\n          <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n            <QuestionsList className=\"h-[550px]\" currentQuestion={0} />\n            <div className=\"mt-6 md:mt-0\">\n              <DIDAvatar className=\"w-[300px] h-[300px]\" />\n            </div>\n          </div>\n\n          <div className=\"flex justify-center mt-10 gap-4\">\n            <Button\n              variant=\"default\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={startInterview}\n            >\n              Start Interview\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          </div>\n          \n          <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n            Ready to begin\n          </div>\n        </InterviewLayout>\n      </div>\n    );\n  }\n\n  if (isInterviewComplete) {\n    return (\n      <div className=\"h-screen\">\n        <JobInfoCard />\n        <InterviewLayout>\n          <div className=\"flex flex-col items-center justify-center h-full\">\n            <div className=\"text-center mb-8\">\n              <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n              <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">\n                Interview Completed!\n              </h2>\n              <p className=\"text-gray-600\">\n                Thank you for completing the interview. Your responses have been recorded.\n              </p>\n            </div>\n            \n            <Button\n              variant=\"default\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={() => onNext?.()}\n            >\n              View Results\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          </div>\n        </InterviewLayout>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" currentQuestion={currentQuestion} />\n          <div className=\"mt-6 md:mt-0\">\n            <DIDAvatar\n              className=\"w-[300px] h-[300px]\"\n              text={currentQuestionText}\n              videoUrl={currentVideoUrl}\n              onVideoReady={handleVideoReady}\n              onVideoEnd={handleVideoEnd}\n              isLoading={isLoading}\n              preloadingService={preloadingServiceRef.current || undefined}\n              showPreloadingStatus={true}\n              priority={1000 - currentQuestion} // Higher priority for current questions\n            />\n          </div>\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          {showSubmitButton && !isLoading ? (\n            <Button\n              variant=\"default\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={handleSubmitAnswer}\n            >\n              {currentQuestion < questions.length ? \"Next Question\" : \"Finish Interview\"}\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          ) : (\n            <div className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500\">\n              {isLoading ? (\n                <div className=\"flex items-center gap-2\">\n                  <Clock className=\"w-4 h-4 animate-spin\" />\n                  {currentVideoUrl ? \"Loading video...\" : \"Preparing question...\"}\n                </div>\n              ) : (\n                \"Listen to the question\"\n              )}\n            </div>\n          )}\n        </div>\n\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          Question {currentQuestion} of {questions.length}\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewWithDID;\n"], "names": [], "mappings": ";;;AAuCY;;AAtCZ;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAkBA,MAAM,mBAAoD,CAAC,EACzD,MAAM,EACN,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC7B;;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAElE,MAAM,YAAY;QAChB,CAAC,8JAA8J,CAAC;QAChK;QACA;QACA;QACA;KACD;IAED,qCAAqC;IACrC,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1C;QACA;QACA;QACA,QAAQ,0FAAuC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IAAI;QAC1E,WAAW;IACb;IAEA,MAAM,iBAAiB;QACrB,sBAAsB;QACtB,MAAM,YAAY,gBAAgB;IACpC;IAEA,MAAM,mBAAmB;QACvB,sEAAsE;QACtE,WAAW;YACT,oBAAoB;QACtB,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,sEAAsE;QACtE,IAAI,CAAC,kBAAkB;YACrB,oBAAoB;QACtB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,cAAc,EAAE;YAC/B,oBAAoB;YAEpB,wBAAwB;YACxB,MAAM,UAAU,MAAM,YAAY,YAAY;YAC9C,IAAI,CAAC,SAAS;gBACZ,sBAAsB;gBACtB;YACF;QACF,OAAO;YACL,sBAAsB;YACtB;QACF;IACF;IAEA,MAAM,sBAAsB,YAAY,cAAc,IAAI,YAAY,oBAAoB,IAAI,UAAU,MAAM;IAE9G,IAAI,CAAC,oBAAoB;QACvB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;8BACZ,6LAAC,iIAAA,CAAA,UAAe;;sCACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+HAAA,CAAA,UAAa;oCAAC,WAAU;oCAAY,iBAAiB;;;;;;8CACtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2HAAA,CAAA,UAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;oCACV;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;sCAA+D;;;;;;;;;;;;;;;;;;IAMtF;IAEA,IAAI,qBAAqB;QACvB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;8BACZ,6LAAC,iIAAA,CAAA,UAAe;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM;;oCAChB;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BACZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;gCAAY,iBAAiB;;;;;;0CACtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2HAAA,CAAA,UAAS;oCACR,WAAU;oCACV,MAAM;oCACN,UAAU;oCACV,cAAc;oCACd,YAAY;oCACZ,WAAW;oCACX,mBAAmB,qBAAqB,OAAO,IAAI;oCACnD,sBAAsB;oCACtB,UAAU,OAAO;;;;;;;;;;;;;;;;;kCAKvB,6LAAC;wBAAI,WAAU;kCACZ,oBAAoB,CAAC,0BACpB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;;gCAER,kBAAkB,UAAU,MAAM,GAAG,kBAAkB;8CACxD,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;iDAGxB,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,kBAAkB,qBAAqB;;;;;;uCAG1C;;;;;;;;;;;kCAMR,6LAAC;wBAAI,WAAU;;4BAA+D;4BAClE;4BAAgB;4BAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;AAKzD;GA9KM;;QAiBgB,mIAAA,CAAA,0BAAuB;;;KAjBvC;uCAgLS", "debugId": null}}, {"offset": {"line": 2253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;KAhBM;uCAiBS", "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport VideoTranscript from \"@/components/VideoTranscript\";\n\ntype FinishInterviewProps = {\n  onNext?: () => void;\n};\n\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList />\n          <CandidateImage className=\"w-[265px]\" />\n          <VideoTranscript />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Finish Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default FinishInterview;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0CACd,6LAAC,gIAAA,CAAA,UAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 2418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,6LAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 2642, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 2704, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,6LAAC,sIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;4BAA8F;0CACrF,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 2933, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateImage from \"@/components/CandidateImage\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateImage className=\"w-[265px]\" />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0BACd,6LAAC,iIAAA,CAAA,UAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,UAAa;;;;;sCACd,6LAAC,gIAAA,CAAA,UAAc;4BAAC,WAAU;;;;;;sCAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,6LAAC,uIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport InterviewWithDID from \"@/components/interview/InterviewWithDID\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewWithDID\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return <div>{renderCurrentComponent()}</div>;\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAeA,MAAM,YAAY;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,6LAAC,+IAAA,CAAA,UAAgB;oBACf,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAEvD,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBAAO,6LAAC;kBAAK;;;;;;AACf;GA9BM;KAAA;uCAgCS", "debugId": null}}]}