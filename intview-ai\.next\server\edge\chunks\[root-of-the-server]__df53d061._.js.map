{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport GitHub from \"next-auth/providers/github\";\r\nimport Google from \"next-auth/providers/google\";\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [GitHub, Google],\r\n  secret: process.env.AUTH_SECRET,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QAAC,6JAAA,CAAA,UAAM;QAAE,6JAAA,CAAA,UAAM;KAAC;IAC3B,QAAQ,QAAQ,GAAG,CAAC,WAAW;AACjC"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["export { auth as middleware } from \"@/auth\";\r\n"], "names": [], "mappings": ";AAAA"}}]}