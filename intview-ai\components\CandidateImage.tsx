import Image from "next/image";
import AvatarImage from "@/public/images/avator.png";

type CandidateImageProps = {
  className?: string;
};

const CandidateImage = ({ className }: CandidateImageProps) => {
  return (
    <div className="mt-6 md:mt-0">
      <Image
        src={AvatarImage}
        alt="Interviewee"
        className={`rounded-lg object-cover ${className}`}
        width={300}
        height={300}
      />
    </div>
  );
};

export default CandidateImage;
