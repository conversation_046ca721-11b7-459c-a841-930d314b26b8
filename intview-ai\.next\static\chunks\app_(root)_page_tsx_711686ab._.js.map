{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/page.tsx"], "sourcesContent": ["\"use client\";\r\n// import { Button } from \"@/components/ui/button\";\r\n// import ROUTES from \"@/constants/routes\";\r\n// import { useRouter } from \"next/navigation\";\r\n\r\nconst Home = () => {\r\n  // const router = useRouter();\r\n  return (\r\n    <div>\r\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Dashboard</h1>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n          <h2 className=\"text-xl font-semibold mb-2\">\r\n            Welcome to AI Interview\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Get started with your interview preparation.\r\n          </p>\r\n        </div>\r\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n          <h2 className=\"text-xl font-semibold mb-2\">Job Posts</h2>\r\n          <p className=\"text-gray-600\">\r\n            Manage and view your job applications.\r\n          </p>\r\n        </div>\r\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n          <h2 className=\"text-xl font-semibold mb-2\">Analytics</h2>\r\n          <p className=\"text-gray-600\">Track your interview performance.</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "names": [], "mappings": ";;;;AAAA;;AACA,mDAAmD;AACnD,2CAA2C;AAC3C,+CAA+C;AAE/C,MAAM,OAAO;IACX,8BAA8B;IAC9B,qBACE,6LAAC;;0BACC,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAG3C,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAKvC;KA3BM;uCA6BS", "debugId": null}}]}