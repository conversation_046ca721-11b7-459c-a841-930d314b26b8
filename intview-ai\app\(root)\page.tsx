"use client";
// import { Button } from "@/components/ui/button";
// import ROUTES from "@/constants/routes";
// import { useRouter } from "next/navigation";

const Home = () => {
  // const router = useRouter();
  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-2">
            Welcome to AI Interview
          </h2>
          <p className="text-gray-600">
            Get started with your interview preparation.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-2">Job Posts</h2>
          <p className="text-gray-600">
            Manage and view your job applications.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-2">Analytics</h2>
          <p className="text-gray-600">Track your interview performance.</p>
        </div>
      </div>
    </div>
  );
};

export default Home;
