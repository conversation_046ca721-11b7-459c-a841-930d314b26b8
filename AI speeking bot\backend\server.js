const express = require('express');
const cors = require('cors');
const fs = require('fs');
const speech = require('@google-cloud/speech');
const axios = require('axios');
const app = express();
const port = 5000;

// Gemini API configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyDCchjks7flBkHP2orTVXdqUGVgE-okqTQ';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Path to your JSON key (using the existing googleCloud.json file)
const client = new speech.SpeechClient({
  keyFilename: '../googleCloud.json',
});

app.post('/speech-to-text', async (req, res) => {
  try {
    const audio = req.body.audioBase64;
    
    if (!audio) {
      return res.status(400).json({ error: 'No audio data provided' });
    }

    const audioBuffer = Buffer.from(audio, 'base64');
    const audioBytes = audioBuffer.toString('base64');

    const audioConfig = {
      content: audioBytes,
    };

    const config = {
      encoding: 'WEBM_OPUS', // Changed from LINEAR16 to support web audio
      sampleRateHertz: 48000, // Changed to match typical web audio
      languageCode: 'en-US',
      enableAutomaticPunctuation: true,
      model: 'latest_long', // Use latest model for better accuracy
    };

    const request = {
      audio: audioConfig,
      config: config,
    };

    const [response] = await client.recognize(request);
    const transcription = response.results
      .map(result => result.alternatives[0].transcript)
      .join('\n');
    
    console.log('Transcription:', transcription);
    res.json({ transcription });
  } catch (err) {
    console.error('Speech recognition error:', err);
    res.status(500).json({ 
      error: 'Speech recognition failed',
      details: err.message 
    });
  }
});

app.post('/api/evaluate', async (req, res) => {
  try {
    const { question, answer } = req.body;

    if (!question || !answer) {
      return res.status(400).json({ error: 'Question and answer are required' });
    }

    const prompt = `You're an AI interview evaluator. Evaluate the candidate's response to the following question:

Question: ${question}
Answer: ${answer}

Provide a score out of 10 and give brief constructive feedback.
Respond in JSON format only:
{
  "score": x,
  "feedback": "..."
}`;

    // Prepare Gemini API request
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 200
      }
    };

    // Make request to Gemini API
    const response = await axios.post(GEMINI_API_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': GEMINI_API_KEY
      }
    });

    console.log('Gemini API response received');

    // Extract the generated content
    let content = response.data.candidates[0].content.parts[0].text;

    // Clean the response - remove markdown code blocks if present
    content = content.replace(/```json\s*/g, '').replace(/```\s*/g, '').trim();

    try {
      const evaluation = JSON.parse(content);
      res.json({ evaluation: content, parsed: evaluation });
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', content);
      res.status(500).json({
        error: 'Failed to parse AI response',
        rawResponse: content
      });
    }

  } catch (error) {
    console.error('Gemini API error:', error);
    res.status(500).json({
      error: 'Failed to evaluate answer',
      details: error.response?.data || error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Speech-to-text and interview evaluation server is running with Gemini AI' });
});

app.listen(port, () => {
  console.log(`Speech-to-text server running on http://localhost:${port}`);
});
