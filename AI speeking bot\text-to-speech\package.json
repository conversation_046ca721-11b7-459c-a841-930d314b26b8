{"name": "text-to-speech", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "cd backend && npm start", "server:dev": "cd backend && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run server:dev\""}, "dependencies": {"@google-cloud/speech": "^7.2.0", "@tailwindcss/vite": "^4.1.11", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^5.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.534.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}