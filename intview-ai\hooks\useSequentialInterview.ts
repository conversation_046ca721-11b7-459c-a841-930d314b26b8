"use client";
import { useState, useCallback, useEffect, useRef } from "react";

// Types
interface InterviewQuestion {
  id: string;
  text: string;
  videoUrl?: string;
  isPreloading?: boolean;
  preloadError?: string;
}

interface InterviewSession {
  id: string;
  startTime: Date;
  currentQuestionIndex: number;
  questions: InterviewQuestion[];
  responses: Array<{
    questionId: string;
    answer: string;
    timestamp: Date;
  }>;
  status: 'preparing' | 'in_progress' | 'completed';
  endTime?: Date;
}

interface UseSequentialInterviewOptions {
  didConfig?: {
    apiKey: string;
    sourceUrl?: string;
  };
  questions?: string[];
  autoGenerateVideos?: boolean;
  preloadCount?: number; // Number of questions to preload ahead
}

interface UseSequentialInterviewState {
  session: InterviewSession | null;
  currentQuestion: InterviewQuestion | null;
  currentVideoUrl: string | null;
  isGeneratingVideo: boolean;
  isSubmitting: boolean;
  userAnswer: string;
  error: string | null;
  preloadingProgress: number; // 0-100
  isPreparingInterview: boolean;
}

// Default questions
const DEFAULT_INTERVIEW_QUESTIONS = [
  "Hello, welcome to your interview. Let's start with our first question: Tell us about yourself and your background.",
  "What are your key strengths and how do they relate to this role?",
  "Why do you want to work in this position, and what interests you about it?",
  "Where do you see yourself in 5 years, and how does this role fit into your career goals?",
  "Thank you for your responses. Do you have any questions about the role or our company before we conclude?"
];

// D-ID API Service
const DID_API_URL = "https://api.d-id.com/talks";

class DIDApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'DIDApiError';
  }
}

const generateQuestionVideo = async (text: string, apiKey: string, sourceUrl?: string): Promise<string> => {
  if (!text.trim()) {
    throw new DIDApiError('Question text is required');
  }

  const payload = {
    script: {
      type: "text",
      input: text.trim(),
    },
    ...(sourceUrl && { source_url: sourceUrl }),
  };

  try {
    // Create talk
    const response = await fetch(DID_API_URL, {
      method: "POST",
      headers: {
        Authorization: `Basic ${btoa(apiKey)}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new DIDApiError(`Failed to create talk: ${response.statusText}`, response.status);
    }

    const data = await response.json();
    const talkId = data.id;

    // Poll for completion
    let attempts = 0;
    const maxAttempts = 30; // 90 seconds max wait time

    while (attempts < maxAttempts) {
      const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
        headers: {
          Authorization: `Basic ${btoa(apiKey)}`,
          "Content-Type": "application/json",
        },
      });

      const pollData = await pollResp.json();

      if (pollData.status === "done" && pollData.result_url) {
        return pollData.result_url;
      } else if (pollData.status === "error") {
        throw new DIDApiError("Video generation failed");
      }

      await new Promise((resolve) => setTimeout(resolve, 3000));
      attempts++;
    }

    throw new DIDApiError("Video generation timed out");
  } catch (error) {
    console.error("D-ID API Error:", error);
    throw error instanceof DIDApiError ? error : new DIDApiError(
      error instanceof Error ? error.message : "Unknown error generating video"
    );
  }
};

export const useSequentialInterview = (options: UseSequentialInterviewOptions = {}) => {
  const {
    didConfig,
    questions = DEFAULT_INTERVIEW_QUESTIONS,
    autoGenerateVideos = true,
    preloadCount = 2, // Preload 2 questions ahead by default
  } = options;

  const [state, setState] = useState<UseSequentialInterviewState>({
    session: null,
    currentQuestion: null,
    currentVideoUrl: null,
    isGeneratingVideo: false,
    isSubmitting: false,
    userAnswer: '',
    error: null,
    preloadingProgress: 0,
    isPreparingInterview: false,
  });

  // Keep track of preloading operations
  const preloadingRef = useRef<Map<string, Promise<string>>>(new Map());
  const abortControllerRef = useRef<AbortController | null>(null);

  const updateState = useCallback((updates: Partial<UseSequentialInterviewState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Background preloading function
  const preloadQuestionVideos = useCallback(async (
    questionsToPreload: InterviewQuestion[],
    startIndex: number = 0
  ) => {
    if (!didConfig?.apiKey || !autoGenerateVideos) return;

    const endIndex = Math.min(startIndex + preloadCount, questionsToPreload.length);
    const questionsSlice = questionsToPreload.slice(startIndex, endIndex);
    
    let completed = 0;
    const total = questionsSlice.length;

    for (const question of questionsSlice) {
      if (question.videoUrl || question.isPreloading) continue;

      try {
        // Mark as preloading
        question.isPreloading = true;
        
        // Start preloading
        const preloadPromise = generateQuestionVideo(
          question.text,
          didConfig.apiKey,
          didConfig.sourceUrl
        );
        
        preloadingRef.current.set(question.id, preloadPromise);
        
        const videoUrl = await preloadPromise;
        question.videoUrl = videoUrl;
        question.isPreloading = false;
        
        completed++;
        updateState({ preloadingProgress: (completed / total) * 100 });
        
      } catch (error) {
        console.error(`Failed to preload question ${question.id}:`, error);
        question.preloadError = error instanceof Error ? error.message : 'Preload failed';
        question.isPreloading = false;
      }
    }
  }, [didConfig, autoGenerateVideos, preloadCount, updateState]);

  // Start interview with background preloading
  const startInterview = useCallback(async (customQuestions?: string[]) => {
    try {
      updateState({ isPreparingInterview: true, error: null });
      
      const interviewQuestions = customQuestions || questions;
      const questionObjects: InterviewQuestion[] = interviewQuestions.map((text, index) => ({
        id: `question_${index}`,
        text,
      }));

      const session: InterviewSession = {
        id: `interview_${Date.now()}`,
        startTime: new Date(),
        currentQuestionIndex: 0,
        questions: questionObjects,
        responses: [],
        status: 'preparing',
      };

      const firstQuestion = questionObjects[0];
      let firstVideoUrl: string | null = null;

      // Generate first question video immediately
      if (autoGenerateVideos && firstQuestion && didConfig?.apiKey) {
        updateState({ isGeneratingVideo: true });
        try {
          firstVideoUrl = await generateQuestionVideo(
            firstQuestion.text,
            didConfig.apiKey,
            didConfig.sourceUrl
          );
          firstQuestion.videoUrl = firstVideoUrl;
        } catch (error) {
          console.error('Failed to generate first question video:', error);
          firstQuestion.preloadError = error instanceof Error ? error.message : 'Video generation failed';
        }
        updateState({ isGeneratingVideo: false });
      }

      // Start background preloading of remaining questions
      if (questionObjects.length > 1) {
        preloadQuestionVideos(questionObjects, 1); // Start from second question
      }

      updateState({
        session: { ...session, status: 'in_progress' },
        currentQuestion: firstQuestion,
        currentVideoUrl: firstVideoUrl,
        userAnswer: '',
        isPreparingInterview: false,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start interview';
      updateState({
        error: `Failed to start interview: ${errorMessage}`,
        isPreparingInterview: false,
      });
    }
  }, [questions, autoGenerateVideos, didConfig, updateState, preloadQuestionVideos]);

  // Submit answer and move to next question
  const submitAnswer = useCallback(async (answer: string) => {
    if (!state.session || !state.currentQuestion) {
      throw new Error('No active interview session');
    }

    updateState({ isSubmitting: true });

    try {
      // Save the response
      const response = {
        questionId: state.currentQuestion.id,
        answer: answer.trim(),
        timestamp: new Date(),
      };

      const updatedResponses = [...state.session.responses, response];
      const nextIndex = state.session.currentQuestionIndex + 1;

      if (nextIndex >= state.session.questions.length) {
        // Interview completed
        const completedSession: InterviewSession = {
          ...state.session,
          currentQuestionIndex: nextIndex,
          responses: updatedResponses,
          status: 'completed',
          endTime: new Date(),
        };

        updateState({
          session: completedSession,
          currentQuestion: null,
          currentVideoUrl: null,
          isSubmitting: false,
        });
        return;
      }

      // Move to next question
      const nextQuestion = state.session.questions[nextIndex];
      let nextVideoUrl = nextQuestion.videoUrl || null;

      // If video isn't ready yet, wait for it
      if (!nextVideoUrl && preloadingRef.current.has(nextQuestion.id)) {
        updateState({ isGeneratingVideo: true });
        try {
          nextVideoUrl = await preloadingRef.current.get(nextQuestion.id)!;
          nextQuestion.videoUrl = nextVideoUrl;
        } catch (error) {
          console.error('Failed to get preloaded video:', error);
        }
        updateState({ isGeneratingVideo: false });
      }

      // Continue preloading ahead
      const preloadStartIndex = nextIndex + 1;
      if (preloadStartIndex < state.session.questions.length) {
        preloadQuestionVideos(state.session.questions, preloadStartIndex);
      }

      const updatedSession: InterviewSession = {
        ...state.session,
        currentQuestionIndex: nextIndex,
        responses: updatedResponses,
      };

      updateState({
        session: updatedSession,
        currentQuestion: nextQuestion,
        currentVideoUrl: nextVideoUrl,
        userAnswer: '',
        isSubmitting: false,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit answer';
      updateState({
        error: `Failed to submit answer: ${errorMessage}`,
        isSubmitting: false,
      });
    }
  }, [state.session, state.currentQuestion, updateState, preloadQuestionVideos]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      preloadingRef.current.clear();
    };
  }, []);

  // Computed values
  const isInterviewActive = state.session?.status === 'in_progress';
  const isInterviewCompleted = state.session?.status === 'completed';
  const currentQuestionNumber = state.session ? state.session.currentQuestionIndex + 1 : 0;
  const totalQuestions = state.session?.questions.length || 0;
  const progress = totalQuestions > 0 ? (currentQuestionNumber / totalQuestions) * 100 : 0;

  return {
    // State
    ...state,
    // Actions
    startInterview,
    submitAnswer,
    setUserAnswer: (answer: string) => updateState({ userAnswer: answer }),
    clearError: () => updateState({ error: null }),
    // Computed values
    isInterviewActive,
    isInterviewCompleted,
    currentQuestionNumber,
    totalQuestions,
    progress,
    // Utilities
    canSubmit: !state.isSubmitting && !state.isGeneratingVideo && state.userAnswer.trim().length > 0,
    hasError: !!state.error,
  };
};
