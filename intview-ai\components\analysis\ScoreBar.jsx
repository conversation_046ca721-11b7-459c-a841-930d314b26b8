const ScoreBar = ({ label, value, color = "bg-orange-500" }) => {
  return (
    <div className="mb-2">
      <div className="flex justify-between text-sm mb-1">
        <span className="mb-1">{label}</span>
        <span>{value}/100</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        <div
          className={`h-2.5 rounded-full ${color}`}
          style={{ width: `${value}%` }}
        ></div>
      </div>
    </div>
  );
};

export default ScoreBar;
