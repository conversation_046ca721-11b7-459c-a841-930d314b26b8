{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_b6e252b8.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_b6e252b8-module__fclFkG__className\",\n  \"variable\": \"poppins_b6e252b8-module__fclFkG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_b6e252b8.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22variable%22:%22--font-poppins%22,%22subsets%22:[%22latin%22],%22preload%22:true,%22fallback%22:[%22Helvetica%22,%22Arial%22,%22sans-serif%22],%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', Helvetica, Arial, sans-serif\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/spacegrotesk_5a4c50b3.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"spacegrotesk_5a4c50b3-module__ecgvsW__className\",\n  \"variable\": \"spacegrotesk_5a4c50b3-module__ecgvsW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/spacegrotesk_5a4c50b3.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/SpaceGroteskVF.ttf%22,%22variable%22:%22--font-space-grotesk%22,%22weight%22:%22300%20400%20500%20600%20700%22}],%22variableName%22:%22spaceGrotesk%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'spaceGrotesk', 'spaceGrotesk Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wIAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,wIAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wIAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/Theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/context/Theme.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/context/Theme.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/Theme.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/context/Theme.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/context/Theme.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiQ,GAC9R,+BACA", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport GitHub from \"next-auth/providers/github\";\r\nimport Google from \"next-auth/providers/google\";\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [GitHub, Google],\r\n  secret: process.env.AUTH_SECRET,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QAAC,qJAAA,CAAA,UAAM;QAAE,qJAAA,CAAA,UAAM;KAAC;IAC3B,QAAQ,QAAQ,GAAG,CAAC,WAAW;AACjC", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/layout.tsx"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from \"next\";\r\nimport { Toaster } from \"@/components/ui/sonner\";\r\n\r\nimport { Poppins } from \"next/font/google\";\r\nimport LocalFonts from \"next/font/local\";\r\nimport \"./globals.css\";\r\nimport ThemeProvider from \"@/context/Theme\";\r\nimport { SessionProvider } from \"next-auth/react\";\r\nimport { auth } from \"@/auth\";\r\n\r\nconst poppins = Poppins({\r\n  variable: \"--font-poppins\",\r\n  subsets: [\"latin\"],\r\n  preload: true,\r\n  fallback: [\"Helvetica\", \"Arial\", \"sans-serif\"],\r\n  weight: [\"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\r\n});\r\n\r\nconst spaceGrotesk = LocalFonts({\r\n  src: \"./fonts/SpaceGroteskVF.ttf\",\r\n  variable: \"--font-space-grotesk\",\r\n  weight: \"300 400 500 600 700\",\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Interview AI\",\r\n  description: `A community driven platform for asking and answering programming questions. Get help, share knowledge \r\n  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, \r\n  data structures, and more.`,\r\n  icons: {\r\n    icon: \"/images/site-logo.svg\",\r\n  },\r\n};\r\n\r\nconst RootLayout = async ({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) => {\r\n  const session = await auth();\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning>\r\n      <SessionProvider session={session}>\r\n        <body\r\n          className={`${poppins.className} ${spaceGrotesk.variable} antialiased`}\r\n        >\r\n          <ThemeProvider attribute={\"class\"} defaultTheme=\"light\">\r\n            {children}\r\n          </ThemeProvider>\r\n          <Toaster />\r\n        </body>\r\n      </SessionProvider>\r\n    </html>\r\n  );\r\n};\r\n\r\nexport default RootLayout;\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAKA;AACA;AACA;;;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa,CAAC;;4BAEY,CAAC;IAC3B,OAAO;QACL,MAAM;IACR;AACF;AAEA,MAAM,aAAa,OAAO,EACxB,QAAQ,EAGR;IACA,MAAM,UAAU,MAAM,CAAA,GAAA,oGAAA,CAAA,OAAI,AAAD;IACzB,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC,qIAAA,CAAA,kBAAe;YAAC,SAAS;sBACxB,cAAA,8OAAC;gBACC,WAAW,GAAG,2IAAA,CAAA,UAAO,CAAC,SAAS,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;;kCAEtE,8OAAC,iHAAA,CAAA,UAAa;wBAAC,WAAW;wBAAS,cAAa;kCAC7C;;;;;;kCAEH,8OAAC,2HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}]}