{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.addMissingImports": "explicit"}, "prettier.tabWidth": 2, "prettier.useTabs": false, "prettier.semi": true, "prettier.singleQuote": false, "prettier.jsxSingleQuote": false, "prettier.trailingComma": "es5", "prettier.arrowParens": "always", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "files.exclude": {"**/.vscode": true, "**/.next": true, "**/node_modules": true, "next-env.d.ts": true}}