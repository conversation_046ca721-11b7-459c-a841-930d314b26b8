{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight } from \"lucide-react\";\nimport React, { useState } from \"react\";\n\ntype InterviewInstructionsProps = {\n  candidateName?: string;\n  jobTitle?: string;\n  languages?: string[];\n  instructions?: string[];\n  environmentChecklist?: string[];\n  disclaimers?: string[];\n  onNext?: () => void;\n};\n\nconst defaultInstructions = [\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n];\n\nconst defaultEnvironment = [\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n];\n\nconst defaultDisclaimers = [\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\n];\n\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\n  candidateName = \"Jonathan\",\n  jobTitle = \"Insurance Agent\",\n  languages = [\"English\", \"Chinese\"],\n  instructions = defaultInstructions,\n  environmentChecklist = defaultEnvironment,\n  disclaimers = defaultDisclaimers,\n  onNext,\n}) => {\n  const [isChecked, setIsChecked] = useState(false);\n\n  return (\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\n        <p className=\"font-semibold mb-8 text-xl\">\n          Instructions for Interview!\n        </p>\n        <div className=\"space-y-6\">\n          <div>\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\n            <p className=\"text-sm mb-4\">\n              As part of the process you are required to complete an AI video\n              assessment for the role of the {jobTitle}.\n            </p>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {languages.map((language, index) => (\n                <li key={index}>{language}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {instructions.map((instruction, index) => (\n                <li key={index}>{instruction}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {environmentChecklist.map((item, index) => (\n                <li key={index}>{item}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {disclaimers.map((disclaimer, index) => (\n                <li key={index}>{disclaimer}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div className=\"flex items-start gap-2 mt-6\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              checked={isChecked}\n              onChange={(e) => setIsChecked(e.target.checked)}\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n            />\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\n              By checking this box, you agree with AI Interview{\" \"}\n              <span className=\"text-primary cursor-pointer font-medium\">\n                Terms of use\n              </span>\n              .\n            </label>\n          </div>\n          <div className=\"flex justify-center\">\n            <Button\n              disabled={!isChecked}\n              variant=\"default\"\n              size=\"lg\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={() => onNext && onNext()}\n            >\n            Proceed\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InterviewInstructions;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,6LAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA9FM;KAAA;uCAgGS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAoC;sDAClC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["type QuestionsListProps = {\r\n  currentQuestion?: number;\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  currentQuestion = 1,\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,kBAAkB,CAAC,EACnB,SAAS,EACU;IACnB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,6LAAC;gCAAK,WAAU;;;;;;0CAElB,6LAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;KArDM;uCAuDS", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/images/avator.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 363, height: 663, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAICAYAAADeM14FAAAAk0lEQVR42gGIAHf/AIJ+cP6DgXL/oqaV/46Xdv4AfXhp/3hwZf+IiHb/j5J3/wB0c27/cmdh/399ef+oppj/AExbeP96gpf/UVtv/3l/if8ALDhQ/3qDlf9ka3r/bnB8/wBJU2v/jpWm/7m8xv/Evrz/AFhUW/+in6v/193o/9PZ4/8AYWRs/svT4P/a4+//ytbn/hi3VG7o7wRAAAAAAElFTkSuQmCC\", blurWidth: 4, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateImage.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport AvatarImage from \"@/public/images/avator.png\";\r\n\r\ntype CandidateImageProps = {\r\n  className?: string;\r\n};\r\n\r\nconst CandidateImage = ({ className }: CandidateImageProps) => {\r\n  return (\r\n    <div className=\"mt-6 md:mt-0\">\r\n      <Image\r\n        src={AvatarImage}\r\n        alt=\"Interviewee\"\r\n        className={`rounded-lg object-cover ${className}`}\r\n        width={300}\r\n        height={300}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateImage;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAuB;IACxD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK,uRAAA,CAAA,UAAW;YAChB,KAAI;YACJ,WAAW,CAAC,wBAAwB,EAAE,WAAW;YACjD,OAAO;YACP,QAAQ;;;;;;;;;;;AAIhB;KAZM;uCAcS", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\nimport { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuestionsPageProps = {\n  onNext?: () => void;\n};\n\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateImage />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            size=\"lg\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default QuestionsPage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateImage />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;KAhBM;uCAiBS", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport VideoTranscript from \"@/components/VideoTranscript\";\n\ntype FinishInterviewProps = {\n  onNext?: () => void;\n};\n\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList />\n          <CandidateImage className=\"w-[265px]\" />\n          <VideoTranscript />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Finish Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default FinishInterview;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0CACd,6LAAC,gIAAA,CAAA,UAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 1253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,6LAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,6LAAC,sIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;4BAA8F;0CACrF,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateImage from \"@/components/CandidateImage\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateImage className=\"w-[265px]\" />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0BACd,6LAAC,iIAAA,CAAA,UAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,UAAa;;;;;sCACd,6LAAC,gIAAA,CAAA,UAAc;4BAAC,WAAU;;;;;;sCAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,6LAAC,uIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport InterviewRecording from \"@/components/interview/InterviewRecording\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return <div>{renderCurrentComponent()}</div>;\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAeA,MAAM,YAAY;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,6LAAC,iJAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAEvD,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBAAO,6LAAC;kBAAK;;;;;;AACf;GA9BM;KAAA;uCAgCS", "debugId": null}}]}