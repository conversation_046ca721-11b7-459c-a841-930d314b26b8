import{jsxs as t,jsx as n}from"react/jsx-runtime";import{createContext as o,useContext as r,useMemo as e,Fragment as a,createElement as i,useRef as s,useCallback as c,useLayoutEffect as l,useEffect as u,useInsertionEffect as f,forwardRef as d}from"react";const m=o({}),p=o({strict:!1}),y=o({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),g=o({});function h(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function v(t){return"string"==typeof t||Array.isArray(t)}const w=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function b(t){return h(t.animate)||w.some(n=>v(t[n]))}function S(t){const{initial:n,animate:o}=function(t,n){if(b(t)){const{initial:n,animate:o}=t;return{initial:!1===n||v(n)?n:void 0,animate:v(o)?o:void 0}}return!1!==t.inherit?n:{}}(t,r(g));return e(()=>({initial:n,animate:o}),[x(n),x(o)])}function x(t){return Array.isArray(t)?t.join(" "):t}const M=(t=>n=>"string"==typeof n&&n.startsWith(t))("--"),P={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},T={...P,transform:t=>((t,n,o)=>o>n?n:o<t?t:o)(0,1,t)},k={...P,default:1},O=t=>({test:n=>"string"==typeof n&&n.endsWith(t)&&1===n.split(" ").length,parse:parseFloat,transform:n=>`${n}${t}`}),W=O("deg"),C=O("%"),E=O("px"),L=(()=>({...C,parse:t=>C.parse(t)/100,transform:t=>C.transform(100*t)}))(),A=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],V=(()=>new Set(A))(),j={...P,transform:Math.round},B={borderWidth:E,borderTopWidth:E,borderRightWidth:E,borderBottomWidth:E,borderLeftWidth:E,borderRadius:E,radius:E,borderTopLeftRadius:E,borderTopRightRadius:E,borderBottomRightRadius:E,borderBottomLeftRadius:E,width:E,maxWidth:E,height:E,maxHeight:E,top:E,right:E,bottom:E,left:E,padding:E,paddingTop:E,paddingRight:E,paddingBottom:E,paddingLeft:E,margin:E,marginTop:E,marginRight:E,marginBottom:E,marginLeft:E,backgroundPositionX:E,backgroundPositionY:E,...{rotate:W,rotateX:W,rotateY:W,rotateZ:W,scale:k,scaleX:k,scaleY:k,scaleZ:k,skew:W,skewX:W,skewY:W,distance:E,translateX:E,translateY:E,translateZ:E,x:E,y:E,z:E,perspective:E,transformPerspective:E,opacity:T,originX:L,originY:L,originZ:E},zIndex:j,fillOpacity:T,strokeOpacity:T,numOctaves:j},I=(t,n)=>n&&"number"==typeof t?n.transform(t):t,R=t=>Boolean(t&&t.getVelocity),X={};function Y(t,{layout:n,layoutId:o}){return V.has(t)||t.startsWith("origin")||(n||void 0!==o)&&(!!X[t]||"opacity"===t)}const $={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},D=A.length;function H(t,n,o){const{style:r,vars:e,transformOrigin:a}=t;let i=!1,s=!1;for(const t in n){const o=n[t];if(V.has(t))i=!0;else if(M(t))e[t]=o;else{const n=I(o,B[t]);t.startsWith("origin")?(s=!0,a[t]=n):r[t]=n}}if(n.transform||(i||o?r.transform=function(t,n,o){let r="",e=!0;for(let a=0;a<D;a++){const i=A[a],s=t[i];if(void 0===s)continue;let c=!0;if(c="number"==typeof s?s===(i.startsWith("scale")?1:0):0===parseFloat(s),!c||o){const t=I(s,B[i]);c||(e=!1,r+=`${$[i]||i}(${t}) `),o&&(n[i]=t)}}return r=r.trim(),o?r=o(n,e?"":r):e&&(r="none"),r}(n,t.transform,o):r.transform&&(r.transform="none")),s){const{originX:t="50%",originY:n="50%",originZ:o=0}=a;r.transformOrigin=`${t} ${n} ${o}`}}const F=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Z(t,n,o){for(const r in n)R(n[r])||Y(r,o)||(t[r]=n[r])}function z(t,n){const o={};return Z(o,t.style||{},t),Object.assign(o,function({transformTemplate:t},n){return e(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{}};return H(o,n,t),Object.assign({},o.vars,o.style)},[n])}(t,n)),o}function N(t,n){const o={},r=z(t,n);return t.drag&&!1!==t.dragListener&&(o.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(o.tabIndex=0),o.style=r,o}const U={offset:"stroke-dashoffset",array:"stroke-dasharray"},q={offset:"strokeDashoffset",array:"strokeDasharray"};function _(t,{attrX:n,attrY:o,attrScale:r,pathLength:e,pathSpacing:a=1,pathOffset:i=0,...s},c,l,u){if(H(t,s,l),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:f,style:d}=t;f.transform&&(d.transform=f.transform,delete f.transform),(d.transform||f.transformOrigin)&&(d.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete f.transformBox),void 0!==n&&(f.x=n),void 0!==o&&(f.y=o),void 0!==r&&(f.scale=r),void 0!==e&&function(t,n,o=1,r=0,e=!0){t.pathLength=1;const a=e?U:q;t[a.offset]=E.transform(-r);const i=E.transform(n),s=E.transform(o);t[a.array]=`${i} ${s}`}(f,e,a,i,!1)}const G=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function J(t,n,o,r){const a=e(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};var e;return _(o,n,"string"==typeof(e=r)&&"svg"===e.toLowerCase(),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[n]);if(t.style){const n={};Z(n,t.style,t),a.style={...n,...a.style}}return a}const K=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Q(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||K.has(t)}let tt=t=>!Q(t);try{"function"==typeof(nt=require("@emotion/is-prop-valid").default)&&(tt=t=>t.startsWith("on")?!Q(t):nt(t))}catch{}var nt;const ot=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rt(t){return"string"==typeof t&&!t.includes("-")&&!!(ot.indexOf(t)>-1||/[A-Z]/u.test(t))}function et(t,n,o,{latestValues:r},s,c=!1){const l=(rt(t)?J:N)(n,r,s,t),u=function(t,n,o){const r={};for(const e in t)"values"===e&&"object"==typeof t.values||(tt(e)||!0===o&&Q(e)||!n&&!Q(e)||t.draggable&&e.startsWith("onDrag"))&&(r[e]=t[e]);return r}(n,"string"==typeof t,c),f=t!==a?{...u,...l,ref:o}:{},{children:d}=n,m=e(()=>R(d)?d.get():d,[d]);return i(t,{...f,children:m})}const at=o(null);function it(t){const n=[{},{}];return t?.values.forEach((t,o)=>{n[0][o]=t.get(),n[1][o]=t.getVelocity()}),n}function st(t,n,o,r){if("function"==typeof n){const[e,a]=it(r);n=n(void 0!==o?o:t.custom,e,a)}if("string"==typeof n&&(n=t.variants&&t.variants[n]),"function"==typeof n){const[e,a]=it(r);n=n(void 0!==o?o:t.custom,e,a)}return n}function ct(t){return R(t)?t.get():t}function lt(t,n,o,r){const e={},a=r(t,{});for(const t in a)e[t]=ct(a[t]);let{initial:i,animate:s}=t;const c=b(t),l=function(t){return Boolean(b(t)||t.variants)}(t);n&&l&&!c&&!1!==t.inherit&&(void 0===i&&(i=n.initial),void 0===s&&(s=n.animate));let u=!!o&&!1===o.initial;u=u||!1===i;const f=u?s:i;if(f&&"boolean"!=typeof f&&!h(f)){const n=Array.isArray(f)?f:[f];for(let o=0;o<n.length;o++){const r=st(t,n[o]);if(r){const{transitionEnd:t,transition:n,...o}=r;for(const t in o){let n=o[t];if(Array.isArray(n)){n=n[u?n.length-1:0]}null!==n&&(e[t]=n)}for(const n in t)e[n]=t[n]}}}return e}const ut=t=>(n,o)=>{const e=r(g),a=r(at),i=()=>function({scrapeMotionValuesFromProps:t,createRenderState:n},o,r,e){return{latestValues:lt(o,r,e,t),renderState:n()}}(t,n,e,a);return o?i():function(t){const n=s(null);return null===n.current&&(n.current=t()),n.current}(i)};function ft(t,n,o){const{style:r}=t,e={};for(const a in r)(R(r[a])||n.style&&R(n.style[a])||Y(a,t)||void 0!==o?.getValue(a)?.liveStyle)&&(e[a]=r[a]);return e}const dt=ut({scrapeMotionValuesFromProps:ft,createRenderState:F});const mt=ut({scrapeMotionValuesFromProps:function(t,n,o){const r=ft(t,n,o);for(const o in t)if(R(t[o])||R(n[o])){r[-1!==A.indexOf(o)?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o]=t[o]}return r},createRenderState:G}),pt="undefined"!=typeof window,yt={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},gt={};for(const t in yt)gt[t]={isEnabled:n=>yt[t].some(t=>!!n[t])};const ht=Symbol.for("motionComponentSymbol");function vt(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function wt(t,n,o){return c(r=>{r&&t.onMount&&t.onMount(r),n&&(r?n.mount(r):n.unmount()),o&&("function"==typeof o?o(r):vt(o)&&(o.current=r))},[n])}const bt="data-"+"framerAppearId".replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase();const St=o({}),xt=pt?l:u;function Mt(t,n,o,e,a){const{visualElement:i}=r(g),c=r(p),l=r(at),d=r(y).reducedMotion,m=s(null);e=e||c.renderer,!m.current&&e&&(m.current=e(t,{visualState:n,parent:i,props:o,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:d}));const h=m.current,v=r(St);!h||h.projection||!a||"html"!==h.type&&"svg"!==h.type||function(t,n,o,r){const{layoutId:e,layout:a,drag:i,dragConstraints:s,layoutScroll:c,layoutRoot:l,layoutCrossfade:u}=n;t.projection=new o(t.latestValues,n["data-framer-portal-id"]?void 0:Pt(t.parent)),t.projection.setOptions({layoutId:e,layout:a,alwaysMeasureLayout:Boolean(i)||s&&vt(s),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:c,layoutRoot:l})}(m.current,o,a,v);const w=s(!1);f(()=>{h&&w.current&&h.update(o,l)});const b=o[bt],S=s(Boolean(b)&&!window.MotionHandoffIsComplete?.(b)&&window.MotionHasOptimisedAnimation?.(b));return xt(()=>{h&&(w.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),h.scheduleRenderMicrotask(),S.current&&h.animationState&&h.animationState.animateChanges())}),u(()=>{h&&(!S.current&&h.animationState&&h.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(b)}),S.current=!1),h.enteringChildren=void 0)}),h}function Pt(t){if(t)return!1!==t.options.allowProjection?t.projection:Pt(t.parent)}function Tt(o,{forwardMotionProps:e=!1}={},a,i){a&&function(t){for(const n in t)gt[n]={...gt[n],...t[n]}}(a);const s=rt(o)?mt:dt;function c(a,c){let l;const u={...r(y),...a,layoutId:kt(a)},{isStatic:f}=u,d=S(a),m=s(a,f);if(!f&&pt){r(p).strict;const t=function(t){const{drag:n,layout:o}=gt;if(!n&&!o)return{};const r={...n,...o};return{MeasureLayout:n?.isEnabled(t)||o?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,d.visualElement=Mt(o,m,u,i,t.ProjectionNode)}return t(g.Provider,{value:d,children:[l&&d.visualElement?n(l,{visualElement:d.visualElement,...u}):null,et(o,a,wt(m,d.visualElement,c),m,f,e)]})}c.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const l=d(c);return l[ht]=o,l}function kt({layoutId:t}){const n=r(m).id;return n&&void 0!==t?n+"-"+t:t}function Ot(t,n){return Tt(t,n)}const Wt=Ot("div");export{Wt as MotionDiv};
