"use client";

// Types
export interface VideoCache {
  [key: string]: {
    url: string;
    timestamp: number;
    expiresAt: number;
  };
}

export interface PreloadTask {
  id: string;
  text: string;
  priority: number;
  status: 'pending' | 'loading' | 'completed' | 'failed';
  promise?: Promise<string>;
  error?: string;
}

export interface VideoPreloadingConfig {
  apiKey: string;
  sourceUrl?: string;
  cacheExpiration?: number; // in milliseconds, default 1 hour
  maxConcurrentRequests?: number;
  retryAttempts?: number;
}

// D-ID API constants
const DID_API_URL = "https://api.d-id.com/talks";
const DEFAULT_CACHE_EXPIRATION = 60 * 60 * 1000; // 1 hour
const DEFAULT_MAX_CONCURRENT = 3;
const DEFAULT_RETRY_ATTEMPTS = 2;

export class DIDApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'DIDApiError';
  }
}

export class VideoPreloadingService {
  private cache: VideoCache = {};
  private preloadQueue: PreloadTask[] = [];
  private activeRequests = 0;
  private config: Required<VideoPreloadingConfig>;
  private abortController: AbortController | null = null;

  constructor(config: VideoPreloadingConfig) {
    this.config = {
      ...config,
      cacheExpiration: config.cacheExpiration || DEFAULT_CACHE_EXPIRATION,
      maxConcurrentRequests: config.maxConcurrentRequests || DEFAULT_MAX_CONCURRENT,
      retryAttempts: config.retryAttempts || DEFAULT_RETRY_ATTEMPTS,
      sourceUrl: config.sourceUrl ?? "", // Ensure sourceUrl is always a string
    };
    
    // Load cache from localStorage if available
    this.loadCacheFromStorage();
    
    // Clean expired cache entries
    this.cleanExpiredCache();
  }

  private generateCacheKey(text: string): string {
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `did_video_${Math.abs(hash)}`;
  }

  private loadCacheFromStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const cached = localStorage.getItem('did_video_cache');
      if (cached) {
        this.cache = JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Failed to load video cache from storage:', error);
      this.cache = {};
    }
  }

  private saveCacheToStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem('did_video_cache', JSON.stringify(this.cache));
    } catch (error) {
      console.warn('Failed to save video cache to storage:', error);
    }
  }

  private cleanExpiredCache(): void {
    const now = Date.now();
    let hasExpired = false;
    
    for (const key in this.cache) {
      if (this.cache[key].expiresAt < now) {
        delete this.cache[key];
        hasExpired = true;
      }
    }
    
    if (hasExpired) {
      this.saveCacheToStorage();
    }
  }

  private async generateVideo(text: string, retryCount = 0): Promise<string> {
    const payload = {
      script: {
        type: "text",
        input: text.trim(),
      },
      ...(this.config.sourceUrl && { source_url: this.config.sourceUrl }),
    };

    try {
      // Create talk
      const response = await fetch(DID_API_URL, {
        method: "POST",
        headers: {
          Authorization: `Basic ${btoa(this.config.apiKey)}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
        signal: this.abortController?.signal,
      });

      if (!response.ok) {
        throw new DIDApiError(`Failed to create talk: ${response.statusText}`, response.status);
      }

      const data = await response.json();
      const talkId = data.id;

      // Poll for completion
      let attempts = 0;
      const maxAttempts = 30; // 90 seconds max wait time

      while (attempts < maxAttempts) {
        if (this.abortController?.signal.aborted) {
          throw new DIDApiError('Request aborted');
        }

        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(this.config.apiKey)}`,
            "Content-Type": "application/json",
          },
          signal: this.abortController?.signal,
        });

        const pollData = await pollResp.json();

        if (pollData.status === "done" && pollData.result_url) {
          return pollData.result_url;
        } else if (pollData.status === "error") {
          throw new DIDApiError("Video generation failed");
        }

        await new Promise((resolve) => setTimeout(resolve, 3000));
        attempts++;
      }

      throw new DIDApiError("Video generation timed out");
    } catch (error) {
      // Retry logic
      if (retryCount < this.config.retryAttempts && !(error instanceof DIDApiError && error.statusCode === 401)) {
        console.warn(`Retrying video generation (attempt ${retryCount + 1}):`, error);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return this.generateVideo(text, retryCount + 1);
      }

      console.error("D-ID API Error:", error);
      throw error instanceof DIDApiError ? error : new DIDApiError(
        error instanceof Error ? error.message : "Unknown error generating video"
      );
    }
  }

  private async processQueue(): Promise<void> {
    while (this.preloadQueue.length > 0 && this.activeRequests < this.config.maxConcurrentRequests) {
      // Find highest priority pending task
      const taskIndex = this.preloadQueue.findIndex(task => task.status === 'pending');
      if (taskIndex === -1) break;

      const task = this.preloadQueue[taskIndex];
      task.status = 'loading';
      this.activeRequests++;

      try {
        const videoUrl = await this.generateVideo(task.text);
        
        // Cache the result
        const cacheKey = this.generateCacheKey(task.text);
        const now = Date.now();
        this.cache[cacheKey] = {
          url: videoUrl,
          timestamp: now,
          expiresAt: now + this.config.cacheExpiration,
        };
        this.saveCacheToStorage();

        task.status = 'completed';
        
        // Remove completed task from queue
        this.preloadQueue.splice(taskIndex, 1);
        
      } catch (error) {
        task.status = 'failed';
        task.error = error instanceof Error ? error.message : 'Unknown error';
        
        // Remove failed task from queue
        this.preloadQueue.splice(taskIndex, 1);
      } finally {
        this.activeRequests--;
      }
    }
  }

  public async getVideoUrl(text: string, priority: number = 0): Promise<string> {
    if (!text.trim()) {
      throw new DIDApiError('Question text is required');
    }

    const cacheKey = this.generateCacheKey(text);
    
    // Check cache first
    const cached = this.cache[cacheKey];
    if (cached && cached.expiresAt > Date.now()) {
      return cached.url;
    }

    // Check if already in queue or being processed
    const existingTask = this.preloadQueue.find(task => task.text === text);
    if (existingTask) {
      if (existingTask.promise) {
        return existingTask.promise;
      }
      // Update priority if higher
      if (priority > existingTask.priority) {
        existingTask.priority = priority;
        // Re-sort queue by priority
        this.preloadQueue.sort((a, b) => b.priority - a.priority);
      }
    } else {
      // Add to queue
      const task: PreloadTask = {
        id: `task_${Date.now()}_${Math.random()}`,
        text,
        priority,
        status: 'pending',
      };
      
      // Create promise for this task
      task.promise = new Promise((resolve, reject) => {
        const checkTask = () => {
          const currentTask = this.preloadQueue.find(t => t.id === task.id);
          if (!currentTask) {
            // Task completed, check cache
            const cached = this.cache[cacheKey];
            if (cached) {
              resolve(cached.url);
            } else {
              reject(new Error('Task completed but no cached result found'));
            }
          } else if (currentTask.status === 'failed') {
            reject(new Error(currentTask.error || 'Video generation failed'));
          } else {
            // Still processing, check again
            setTimeout(checkTask, 500);
          }
        };
        
        setTimeout(checkTask, 100);
      });
      
      this.preloadQueue.push(task);
      // Sort by priority (highest first)
      this.preloadQueue.sort((a, b) => b.priority - a.priority);
    }

    // Start processing queue
    this.processQueue();

    // Return promise for this specific task
    return existingTask?.promise || this.preloadQueue.find(t => t.text === text)?.promise || Promise.reject(new Error('Failed to create task'));
  }

  public preloadVideos(texts: string[], basePriority: number = 0): void {
    texts.forEach((text, index) => {
      const priority = basePriority - index; // Earlier questions get higher priority
      this.getVideoUrl(text, priority).catch(error => {
        console.warn(`Failed to preload video for: "${text.substring(0, 50)}..."`, error);
      });
    });
  }

  public getQueueStatus(): {
    pending: number;
    loading: number;
    completed: number;
    failed: number;
    total: number;
  } {
    const pending = this.preloadQueue.filter(t => t.status === 'pending').length;
    const loading = this.preloadQueue.filter(t => t.status === 'loading').length;
    const completed = this.preloadQueue.filter(t => t.status === 'completed').length;
    const failed = this.preloadQueue.filter(t => t.status === 'failed').length;
    
    return {
      pending,
      loading,
      completed,
      failed,
      total: this.preloadQueue.length,
    };
  }

  public clearCache(): void {
    this.cache = {};
    this.saveCacheToStorage();
  }

  public abort(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.abortController = new AbortController();
    
    // Clear queue
    this.preloadQueue = [];
    this.activeRequests = 0;
  }

  public destroy(): void {
    this.abort();
    this.clearCache();
  }
}
