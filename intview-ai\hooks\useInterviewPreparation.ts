"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { VideoPreloadingService } from "@/lib/videoPreloadingService";

interface PreparationStatus {
  isPreparingInterview: boolean;
  progress: number;
  currentStep: string;
  totalQuestions: number;
  loadedQuestions: number;
  failedQuestions: number;
  estimatedTimeRemaining: number;
  error: string | null;
}

interface UseInterviewPreparationOptions {
  questions: string[];
  candidateName?: string;
  jobTitle?: string;
  apiKey?: string;
  sourceUrl?: string;
  autoStart?: boolean;
}

export const useInterviewPreparation = (options: UseInterviewPreparationOptions) => {
  const {
    questions,
    candidateName = "Candidate",
    jobTitle = "Position",
    apiKey,
    sourceUrl,
    autoStart = false,
  } = options;

  const [status, setStatus] = useState<PreparationStatus>({
    isPreparingInterview: false,
    progress: 0,
    currentStep: "",
    totalQuestions: questions.length,
    loadedQuestions: 0,
    failedQuestions: 0,
    estimatedTimeRemaining: 0,
    error: null,
  });

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string>("");
  const [isInterviewReady, setIsInterviewReady] = useState(false);

  const preloadingServiceRef = useRef<VideoPreloadingService | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);

  // Personalize questions with candidate name and job title
  const personalizedQuestions = questions.map((question, index) => {
    if (index === 0) {
      return question
        .replace(/\{candidateName\}/g, candidateName)
        .replace(/\{jobTitle\}/g, jobTitle);
    }
    return question;
  });

  const updateStatus = useCallback((updates: Partial<PreparationStatus>) => {
    setStatus(prev => ({ ...prev, ...updates }));
  }, []);

  const estimateTimeRemaining = useCallback((completed: number, total: number, startTime: number): number => {
    if (completed === 0) return 0;
    
    const elapsed = Date.now() - startTime;
    const avgTimePerQuestion = elapsed / completed;
    const remaining = total - completed;
    
    return Math.ceil((remaining * avgTimePerQuestion) / 1000);
  }, []);

  const startPreparation = useCallback(async () => {
    if (!apiKey) {
      updateStatus({ 
        error: "API key is required for video generation",
        isPreparingInterview: false 
      });
      return;
    }

    updateStatus({
      isPreparingInterview: true,
      progress: 0,
      currentStep: "Initializing interview preparation...",
      error: null,
    });

    startTimeRef.current = Date.now();

    try {
      // Initialize preloading service
      preloadingServiceRef.current = new VideoPreloadingService({
        apiKey,
        sourceUrl,
        cacheExpiration: 60 * 60 * 1000, // 1 hour
        maxConcurrentRequests: 3,
        retryAttempts: 2,
      });

      updateStatus({ currentStep: "Starting background video generation..." });

      // Start preloading all questions
      preloadingServiceRef.current.preloadVideos(personalizedQuestions, 100);

      // Monitor progress
      progressIntervalRef.current = setInterval(() => {
        if (preloadingServiceRef.current) {
          const queueStatus = preloadingServiceRef.current.getQueueStatus();
          const progress = queueStatus.total > 0 ? 
            (queueStatus.completed / queueStatus.total) * 100 : 0;
          
          const estimatedTime = estimateTimeRemaining(
            queueStatus.completed,
            queueStatus.total,
            startTimeRef.current
          );

          updateStatus({
            progress,
            loadedQuestions: queueStatus.completed,
            failedQuestions: queueStatus.failed,
            estimatedTimeRemaining: estimatedTime,
            currentStep: queueStatus.loading > 0 ? 
              `Generating video ${queueStatus.completed + 1} of ${queueStatus.total}...` :
              queueStatus.completed === queueStatus.total ?
                "All questions ready!" :
                "Preparing questions...",
          });

          // Check if preparation is complete
          if (queueStatus.completed + queueStatus.failed >= queueStatus.total) {
            if (progressIntervalRef.current) {
              clearInterval(progressIntervalRef.current);
              progressIntervalRef.current = null;
            }

            // Load first question
            loadQuestion(0);
            
            updateStatus({
              isPreparingInterview: false,
              currentStep: "Interview ready to start!",
            });
            
            setIsInterviewReady(true);
          }
        }
      }, 500);

    } catch (error) {
      console.error("Failed to start interview preparation:", error);
      updateStatus({
        error: error instanceof Error ? error.message : "Failed to prepare interview",
        isPreparingInterview: false,
      });
    }
  }, [apiKey, sourceUrl, personalizedQuestions, updateStatus, estimateTimeRemaining]);

  const loadQuestion = useCallback(async (questionIndex: number) => {
    if (!preloadingServiceRef.current || questionIndex >= personalizedQuestions.length) {
      return;
    }

    const questionText = personalizedQuestions[questionIndex];
    
    try {
      const videoUrl = await preloadingServiceRef.current.getVideoUrl(
        questionText, 
        1000 - questionIndex // Higher priority for earlier questions
      );
      
      setCurrentVideoUrl(videoUrl);
      setCurrentQuestionIndex(questionIndex);
    } catch (error) {
      console.error(`Failed to load question ${questionIndex}:`, error);
      setCurrentVideoUrl("");
    }
  }, [personalizedQuestions]);

  const nextQuestion = useCallback(async () => {
    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex < personalizedQuestions.length) {
      await loadQuestion(nextIndex);
      return true;
    }
    return false; // Interview completed
  }, [currentQuestionIndex, personalizedQuestions.length, loadQuestion]);

  const getCurrentQuestion = useCallback(() => {
    return personalizedQuestions[currentQuestionIndex] || "";
  }, [personalizedQuestions, currentQuestionIndex]);

  const getProgress = useCallback(() => {
    return personalizedQuestions.length > 0 ? 
      ((currentQuestionIndex + 1) / personalizedQuestions.length) * 100 : 0;
  }, [personalizedQuestions.length, currentQuestionIndex]);

  // Auto-start preparation if enabled
  useEffect(() => {
    if (autoStart && !status.isPreparingInterview && !isInterviewReady) {
      startPreparation();
    }
  }, [autoStart, status.isPreparingInterview, isInterviewReady, startPreparation]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      if (preloadingServiceRef.current) {
        preloadingServiceRef.current.destroy();
      }
    };
  }, []);

  return {
    // Status
    ...status,
    isInterviewReady,
    
    // Current question data
    currentQuestionIndex,
    currentVideoUrl,
    currentQuestionText: getCurrentQuestion(),
    interviewProgress: getProgress(),
    
    // Actions
    startPreparation,
    nextQuestion,
    loadQuestion,
    
    // Utilities
    preloadingService: preloadingServiceRef.current,
    personalizedQuestions,
    isLastQuestion: currentQuestionIndex >= personalizedQuestions.length - 1,
  };
};
