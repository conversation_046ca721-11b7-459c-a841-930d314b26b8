// import JobInfoCard from "@/components/JobInfoCard";
import QuestionsList from "@/components/QuestionsList";
import CandidateImage from "@/components/CandidateImage";
import InterviewLayout from "@/components/InterviewLayout";
import VideoTranscript from "@/components/VideoTranscript";
import InterviewCard from "@/components/InterviewCard";
import ScoreCard from "../analysis/ScoreCard";

const Analysis = () => {
  return (
    <div className="h-screen">
      <InterviewCard />
      <InterviewLayout>
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          <QuestionsList />
          <CandidateImage className="w-[265px]" />
          <VideoTranscript />
        </div>
      </InterviewLayout>
      <ScoreCard />
    </div>
  );
};

export default Analysis;
