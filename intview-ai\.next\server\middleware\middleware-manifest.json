{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kx5fv6OaRf1Y5ArHnAo9aEvVgDWGx4JcYen3hw8UgIA=", "__NEXT_PREVIEW_MODE_ID": "1baf31108901cf22f889401400baa056", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c41fece14d36a6d913295f75644acedc5154ccb460a86f766696ba0f8664d187", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "af21d975780a20bc4581a75be1f455e3e98781bab6870ed1d615d2e4d70e9301"}}}, "instrumentation": null, "functions": {}}